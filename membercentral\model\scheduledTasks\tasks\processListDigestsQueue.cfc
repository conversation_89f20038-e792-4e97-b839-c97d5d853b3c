<cfcomponent output="false" extends="model.scheduledTasks.scheduledTasks">

	<cffunction name="runTask" access="public" returntype="void" output="false">
		<cfargument name="strTask" type="struct" required="true">
		<cfargument name="rc" type="struct" required="true">
		
		<cfset var local = structnew()>

		<cfsetting requesttimeout="600">

		<cfset local.processQueueResult = processQueue()>
		<cfif NOT local.processQueueResult.success>
			<cfthrow message="Error running processQueue()">
		<cfelse>
			<cfset local.historyID = (arguments.strTask.historyID ?: 0)>
			<cfset addBatchIdentifierToHistory(historyID=local.historyID, batchIdentifier="", itemCount=local.processQueueResult.itemCount)>
		</cfif>
	</cffunction>
	
	<cffunction name="processQueue" access="private" output="false" returntype="struct">
		<cfset var local = structnew()>
		<cfset local.returnStruct = { "success":true, "itemCount":0 }>
		<cfset local.objResourceTemplates = createObject("component","model.admin.common.modules.resourceTemplates.resourceTemplate")>

		<cftry>
			<!--- ---------------------- --->
			<!--- grab and process queue --->
			<!--- ---------------------- --->
			<cfstoredproc procedure="queue_listDigests_grabForProcessing" datasource="#application.dsn.platformQueue.dsn#">
				<cfprocresult name="local.qryListDigests" resultset="1">
			</cfstoredproc>

			<!--- loop per list --->
			<cfloop query="local.qryListDigests">
				<cftry>
					<cfset local.mc_siteinfo = application.objSiteInfo.getSiteInfo(local.qryListDigests.sitecode)>
				

					<!--- item must still be in the grabbedForProcessing state for this job. else skip it. --->
					<!--- this is here because we have a check in place that will attempt to clear items "stuck" in the grabbedForProcessing state and those items may be grabbed by another job, causing it to possible be processed twice. --->
					<cfquery name="local.checkQueueItemID" datasource="#application.dsn.platformQueue.dsn#">
						SELECT COUNT(qi.itemID) AS itemCount
						FROM dbo.queue_listDigests as qi
						INNER JOIN dbo.tblQueueStatuses AS qs ON qs.queueStatusID = qi.statusID 
						INNER JOIN dbo.tblQueueTypes AS qt ON qt.queueTypeID = qs.queueTypeID
						where qi.itemID = <cfqueryparam cfsqltype="CF_SQL_IDSTAMP" value="#local.qryListDigests.itemID#">
						AND qt.queueType = 'listDigests'
						AND qs.queueStatus = 'grabbedForProcessing';
					</cfquery>

					<cfif local.checkQueueItemID.itemCount and listFindNoCase("ListThreadIndex,ListThreadDigest",local.qryListDigests.digestType)>
						<!--- UPDATE STATUS --->
						<cfset setListDigestQueueStatus(itemID=local.qryListDigests.itemID, status="Processing")>

						<cfquery name="local.qryListDetailsMC" datasource="#application.dsn.membercentral.dsn#">
							SELECT mcL.listID, mcL.listName, mcL.siteResourceID, template_html.templateID
							FROM dbo.lists_lists AS mcL 
							INNER JOIN dbo.cms_siteResources AS lsr ON mcL.siteResourceID = lsr.siteResourceID AND lsr.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.mc_siteinfo.siteID#">
							INNER JOIN dbo.cms_siteResourceStatuses AS srs on srs.siteResourceStatusID = lsr.siteResourceStatusID AND srs.siteResourceStatusDesc = 'Active'
							INNER JOIN dbo.template_usages AS template_html ON template_html.referenceID = mcL.listID 
								AND template_html.referenceType = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.qryListDigests.digestType#Html">
							WHERE mcL.listName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.qryListDigests.listname#">;
						</cfquery>

						<cfquery name="local.qryListDetailsLYRIS" datasource="#application.dsn.trialslyris1.dsn#">
							SELECT tlaL.name_ AS listName, tlaL.descShort_ AS listDesc
							FROM dbo.lists_format AS tlaLF 
							INNER JOIN dbo.lists_ AS tlaL ON tlaL.name_ = tlaLF.Name
							WHERE tlaLF.name = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.qryListDigests.listname#">
								AND tlaLF.disabled = 0;
						</cfquery>

						<cfquery name="local.qryListDetails" dbtype="query">
							SELECT l1.listID, l1.listName, l1.templateID, l2.listDesc
							FROM [local].qryListDetailsMC l1, [local].qryListDetailsLYRIS l2
							WHERE l2.listName = l1.listName;
						</cfquery>

						<cfquery name="local.qryListRecipients" datasource="#application.dsn.trialslyris1.dsn#">
							SELECT top 1 externalMemberID
							FROM trialslyris1.dbo.members_ m 
							WHERE list_ = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.qryListDigests.listname#">
							AND memberType_ in ('normal','held')
							<cfif local.qryListDigests.digestType eq "ListThreadIndex">
								AND receiveMCThreadIndex = 1;
							<cfelse>
								AND receiveMCThreadDigest = 1;
							</cfif>
						</cfquery>

						<cfif local.qryListDetails.recordCount and (local.qryListRecipients.recordCount or local.qryListDigests.isTestMode)>						

							<cfset local.qryTemplateDetails = local.objResourceTemplates.getResourceTemplateDetails(siteID=local.mc_siteinfo.siteID, templateID=local.qryListDetails.templateID, resourceType='listAdmin')>
							<cfif not local.qryTemplateDetails.recordCount>
								<!--- Skip because valid template is not assigned. --->
								<cfset setListDigestQueueStatus(itemID=local.qryListDigests.itemID, status="Skipped")>
							<cfelse>
								<cfset local.getListsInfoResult = CreateObject("component","model.admin.list.listAdmin").getListsInfo(siteCode=local.qryListDigests.siteCode, listName=local.qryListDigests.listname, digestDate=local.qryListDigests.digestDate, templateType=local.qryListDigests.digestType, templateFormat=local.qryTemplateDetails.templateFormat)>
								<cfset local.digestModel = local.getListsInfoResult.listdata>
								<cfif not local.getListsInfoResult.success>
									<cfthrow>
								</cfif> 
								<cfset local.strRenderedTemplate = local.objResourceTemplates.doRenderResourceTemplate(template=local.qryTemplateDetails.templateContent, model=local.digestModel, templateFormat=local.qryTemplateDetails.templateFormat)>
								
								<cfset local.templateContent = application.objResourceRenderer.qualifyAllLinks(content=local.strRenderedTemplate.content, siteID=local.mc_siteinfo.siteID, qualURL="#local.mc_siteinfo.scheme#://#local.mc_siteinfo.mainhostname#/")>
								<cfset local.templateContent = application.objEmailWrapper.disableClickTrackingForSpecificLinks(htmlcontent=local.templateContent)>

								<cfquery name="local.qryRecipients" datasource="#application.dsn.membercentral.dsn#">
									SET XACT_ABORT, NOCOUNT ON;
									BEGIN TRY

										DECLARE @siteID INT, @listName VARCHAR(200), @resourceTypeID INT, @listSiteResourceID INT, 
											@listID INT, @emailSubject VARCHAR(200), @rawContent VARCHAR(MAX), @contentID INT, @siteResourceID INT, @sendOnDate DATETIME, @contentVersionID INT, 
											@enteredByMemberID INT, @digestType VARCHAR(25), @digestDate DATE, @isTestMode bit;

										SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.mc_siteinfo.siteID#">;
										SET @listName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.qryListDigests.listname#">;
										SET @listID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryListDetails.listID#">;
										SET @listSiteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryListDetailsMC.siteResourceID#">;
										SET @digestType = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.qryListDigests.digestType#">;
										SET @digestDate = <cfqueryparam cfsqltype="CF_SQL_DATE" value="#local.qryListDigests.digestDate#">;
										SET @isTestMode = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#local.qryListDigests.isTestMode#">;
										SET @emailSubject = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="[#local.qryListDigests.listname#] Digest for #dateformat(local.qryListDigests.digestDate, "mmmm d, yyyy")#">;
										SET @rawContent = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#local.templateContent#">;
										SET @sendOnDate = GETDATE();
										SET @resourceTypeID = dbo.fn_getResourceTypeID('ApplicationCreatedContent');

										SELECT @enteredByMemberID = dbo.fn_ams_getMCSystemMemberID();

										EXEC dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@resourceTypeID, @parentSiteResourceID=@listSiteResourceID, 
											@siteResourceStatusID=1, @isHTML=1, @languageID=1, @isActive=1, @contentTitle=@emailSubject, @contentDesc='', 
											@rawContent=@rawContent, @memberID=@enteredByMemberID, @contentID=@contentID OUTPUT, @siteResourceID=@siteResourceID OUTPUT;

										SELECT TOP 1 @contentVersionID = cv.contentVersionID
										FROM dbo.cms_content AS c 
										INNER JOIN dbo.cms_contentLanguages AS cl ON cl.siteID = @siteID
											AND c.contentID = cl.contentID 
											AND cl.languageID = 1
										INNER JOIN dbo.cms_contentVersions AS cv ON cv.siteID = @siteID
											AND cv.contentID = cl.contentID
											AND cv.contentLanguageID = cl.contentLanguageID 
											AND cv.isActive = 1
										WHERE c.siteID = @siteID
										AND c.contentID = @contentID;

										EXEC dbo.lists_sendEmailDigest @siteID=@siteID, @listName=@listName, @digestType=@digestType, @digestDate=@digestDate, @emailSubject=@emailSubject, 
											@rawContent=@rawContent, @contentVersionID=@contentVersionID, @listID=@listID, @recordedByMemberID=@enteredByMemberID, @sendOnDate=@sendOnDate, 
											@isTestMode=@isTestMode;

									END TRY
									BEGIN CATCH
										IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
										EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
									END CATCH
								</cfquery>

								<cfset local.returnStruct.itemCount += local.qryRecipients.recordCount>
								<cfset setListDigestQueueStatus(itemID=local.qryListDigests.itemID, status="Done")>
							</cfif>
						<cfelse>
							<!--- UPDATE STATUS --->
							<cfset setListDigestQueueStatus(itemID=local.qryListDigests.itemID, status="Skipped")>
						</cfif>
					</cfif>
					<cfcatch type="any">
						<cfset retryListDigestQueue(itemID=local.qryListDigests.itemID)>
						<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local, customMessage="Failed attempting to send a specific digest.")>
					</cfcatch>
				</cftry>		
			</cfloop>

			<!--- -------------------------------------------------------- --->
			<!--- post processing - delete any queue items that are done   --->
			<!--- -------------------------------------------------------- --->
			<cftry>
				<cfstoredproc procedure="queue_listDigests_clearDone" datasource="#application.dsn.platformQueue.dsn#">
				</cfstoredproc>
			<cfcatch type="Any">
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			</cfcatch>
			</cftry>		

		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset local.returnStruct.success = false>
		</cfcatch>
		</cftry>			
		
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="setListDigestQueueStatus" access="private" returntype="void" output="false">
		<cfargument name="itemID" type="numeric" required="yes">
		<cfargument name="status" type="string" required="yes">

		<cfset var qryUpdateStatus = "">

		<cfquery name="qryUpdateStatus" datasource="#application.dsn.platformQueue.dsn#">
			SET NOCOUNT ON;

			DECLARE @newstatus INT;
			
			SELECT @newstatus = qs.queueStatusID 
			FROM dbo.tblQueueStatuses AS qs
			INNER JOIN dbo.tblQueueTypes AS qt ON qt.queueTypeID = qs.queueTypeID
			WHERE qt.queueType = 'listDigests'
			AND qs.queueStatus = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.status#">;

			IF @newstatus IS NOT NULL
				UPDATE dbo.queue_listDigests WITH (UPDLOCK, HOLDLOCK)
				SET statusID = @newstatus,
					dateUpdated = GETDATE()
				WHERE itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.itemID#">;
		</cfquery>
	</cffunction>

	<cffunction name="retryListDigestQueue" access="private" output="false" returntype="void">
		<cfargument name="itemID" type="numeric" required="yes">

		<cfset var qryUpdateStatus = "">

		<cfquery name="qryUpdateStatus" datasource="#application.dsn.platformQueue.dsn#">
			SET NOCOUNT ON;

			DECLARE @readyToProcessStatusID int, @nowDate datetime = GETDATE();
			
			SELECT @readyToProcessStatusID = qs.queueStatusID 
			FROM dbo.tblQueueStatuses AS qs
			INNER JOIN dbo.tblQueueTypes AS qt ON qt.queueTypeID = qs.queueTypeID
			WHERE qt.queueType = 'listDigests'
			AND qs.queueStatus = 'ReadyToProcess';

			UPDATE dbo.queue_listDigests WITH (UPDLOCK, HOLDLOCK)
			SET statusID = @readyToProcessStatusID,
				nextAttemptDate = DATEADD(HH,1,@nowDate),
				previousAttempts = previousAttempts + 1,
				dateUpdated = @nowDate
			WHERE itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.itemID#">;
		</cfquery>
	</cffunction>

</cfcomponent>