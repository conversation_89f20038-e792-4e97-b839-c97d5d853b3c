ALTER PROC dbo.queue_clientSurveys_grabForProcessing
@batchSize int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @statusReady int, @statusGrabbed int;

	select @statusReady = qs.queueStatusID 
		from dbo.tblQueueStatuses as qs
		inner join dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'clientSurveys'
		and qs.queueStatus = 'readyToProcess';
	select @statusGrabbed = qs.queueStatusID 
		from dbo.tblQueueStatuses as qs
		inner join dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'clientSurveys'
		and qs.queueStatus = 'grabbedForProcessing';

	IF OBJECT_ID('tempdb..#tmpTblQueueItems_clientSurveys') IS NOT NULL 
		DROP TABLE #tmpTblQueueItems_clientSurveys;
	CREATE TABLE #tmpTblQueueItems_clientSurveys(itemID int, referralID int, callUID varchar(50));

	-- dequeue
	UPDATE qi WITH (UPDLOCK, READPAST)
	SET qi.statusID = @statusGrabbed,
		qi.dateUpdated = getdate()
		OUTPUT inserted.itemID, inserted.referralID, inserted.callUID INTO #tmpTblQueueItems_clientSurveys
	FROM dbo.queue_clientSurveys as qi
	INNER JOIN (
		SELECT top(@BatchSize) qi2.itemID 
		FROM dbo.queue_clientSurveys as qi2
		WHERE qi2.statusID = @statusReady
		ORDER BY qi2.dateAdded, qi2.itemID
	) as batch on batch.itemID = qi.itemID
	WHERE qi.statusID = @statusReady;

	-- final data
	select qidd.* 
	from #tmpTblQueueItems_clientSurveys as qid
	inner join dbo.queue_clientSurveysDetail as qidd on qidd.itemID = qid.itemID
	order by qid.itemID;

	IF OBJECT_ID('tempdb..#tmpTblQueueItems_clientSurveys') IS NOT NULL 
		DROP TABLE #tmpTblQueueItems_clientSurveys;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
