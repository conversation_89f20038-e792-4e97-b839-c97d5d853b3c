ALTER PROC dbo.queue_listDigests_grabForProcessing

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @batchSize INT = 15, @statusReady INT, @statusGrabbed INT;

	SELECT @statusReady = qs.queueStatusID 
		FROM dbo.tblQueueStatuses AS qs
		INNER JOIN dbo.tblQueueTypes AS qt ON qt.queueTypeID = qs.queueTypeID
		WHERE qt.queueType = 'listDigests'
		AND qs.queueStatus = 'readyToProcess';
	SELECT @statusGrabbed = qs.queueStatusID 
		FROM dbo.tblQueueStatuses AS qs
		INNER JOIN dbo.tblQueueTypes AS qt ON qt.queueTypeID = qs.queueTypeID
		WHERE qt.queueType = 'listDigests'
		AND qs.queueStatus = 'grabbedForProcessing';

	IF OBJECT_ID('tempdb..#tmplistDigests') IS NOT NULL 
		DROP TABLE #tmplistDigests;
	CREATE TABLE #tmplistDigests (itemID INT);

	UPDATE qi WITH (UPDLOCK, READPAST)
	SET qi.statusID = @statusGrabbed,
		qi.dateUpdated = GETDATE()
		OUTPUT inserted.itemID INTO #tmplistDigests
	FROM dbo.queue_listDigests AS qi
	INNER JOIN (
		SELECT TOP(@batchSize) qi2.itemID
		FROM dbo.queue_listDigests AS qi2
		WHERE qi2.statusID = @statusReady
		AND qi2.nextAttemptDate < GETDATE()
		ORDER BY qi2.dateAdded, qi2.itemID
	) AS batch ON batch.itemID = qi.itemID
	WHERE qi.statusID = @statusReady;

	SELECT qi.itemID, qi.orgCode,qi.listname,qi.digestType,qi.digestDate,qi.statusID, qi.isTestMode, o.orgID, s.siteID, s.siteCode
	FROM #tmplistDigests AS tmp
	INNER JOIN dbo.queue_listDigests AS qi ON qi.itemID = tmp.itemID
	INNER JOIN memberCentral.dbo.sites AS s ON s.sitecode = qi.orgcode
	INNER JOIN memberCentral.dbo.organizations AS o ON o.orgID = s.orgID
	ORDER BY qi.itemID;

	IF OBJECT_ID('tempdb..#tmplistDigests') IS NOT NULL 
		DROP TABLE #tmplistDigests;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
