<cfcomponent output="false" extends="model.scheduledTasks.scheduledTasks">

	<cffunction name="runTask" access="public" returntype="void" output="false">
		<cfargument name="strTask" type="struct" required="true">
		<cfargument name="rc" type="struct" required="true">
		
		<cfset var local = structnew()>

		<cfsetting requesttimeout="600">

		<cfset local.itemCount = getQueueItemCount()>

		<cfif local.itemCount GT 0>
			<cfset local.success = processQueue()>
			<cfif NOT local.success>
				<cfthrow message="Error running processQueue()">
			</cfif>
		</cfif>

		<cfset local.historyID = (arguments.strTask.historyID ?: 0)>
		<cfset addBatchIdentifierToHistory(historyID=local.historyID, batchIdentifier="", itemCount=local.itemCount)>
	</cffunction>

	<cffunction name="processQueue" access="private" output="false" returntype="boolean">
		<cfset var local = structnew()>
		<cfset local.success = true>
		<cfset local.errCode = 0>
		<cfset local.objAccounting = CreateObject("component","model.system.platform.accounting")>
		<cftry>
			<!--- ---------------------- --->
			<!--- grab and process queue --->
			<!--- ---------------------- --->
			<cfstoredproc procedure="queue_payInvoices_grabForProcessing" datasource="#application.dsn.platformQueue.dsn#">
				<cfprocresult name="local.qryInvoices" resultset="1">
			</cfstoredproc>
			
			<cfset local.MCSystemMemberID = application.objCommon.getMCSystemMemberID()>
	
			<!--- loop per payment --->
			<cfoutput query="local.qryInvoices" group="itemID">
			
				<!--- item must still be in the grabbedForProcessing state for this job. else skip it. --->
				<!--- this is here because we have a check in place that will attempt to clear items "stuck" in the grabbedForProcessing state and those items may be grabbed by another job, causing it to possible be processed twice. --->
				<cfquery name="local.checkItemID" datasource="#application.dsn.platformQueue.dsn#">
					select count(qi.itemID) as itemCount
					from dbo.queue_payInvoices as qi
					INNER JOIN dbo.tblQueueStatuses AS qs ON qs.queueStatusID = qi.statusID 
					where qi.itemID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.qryInvoices.itemID#">
					AND qs.queueStatus = 'grabbedForProcessing'
				</cfquery>
				<cfif local.checkItemID.itemCount>
					<cftry>
						<cfquery name="local.qryUpdateToProcessingPayment" datasource="#application.dsn.platformQueue.dsn#">
							SET NOCOUNT ON;

							DECLARE @statusProcessing int;
							EXEC dbo.queue_getStatusIDbyType @queueType='PayInvoices', @queueStatus='processingPayment', @queueStatusID=@statusProcessing OUTPUT;

							UPDATE dbo.queue_payInvoices
							SET statusID = @statusProcessing,
								dateUpdated = getdate()
							WHERE itemID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.qryInvoices.itemID#">;
						</cfquery>
	
						<!--- if amount needs to be charged --->
						<cfif local.qryInvoices.paymentAmount gt 0>
						
							<!--- ISSUE PAYMENT --->
							<cfscript>
							if (local.qryInvoices.tokenStore EQ 'bankdraft') {
								local.qryInfoOnFile = queryNew("payProfileID,routingNumber,accountNumber,acctType,surchargeEligible","integer,varchar,varchar,varchar,boolean");
								if (queryAddRow(local.qryInfoOnFile)) {
									QuerySetCell(local.qryInfoOnFile,"payProfileID",local.qryInvoices.memberPaymentProfileID);
									QuerySetCell(local.qryInfoOnFile,"routingNumber",local.qryInvoices.routingNumber);
									QuerySetCell(local.qryInfoOnFile,"accountNumber",local.qryInvoices.accountNumber);
									QuerySetCell(local.qryInfoOnFile,"acctType",local.qryInvoices.acctType);
									QuerySetCell(local.qryInfoOnFile,"surchargeEligible",val(local.qryInvoices.surchargeEligible));
								}
							} else {
								local.qryInfoOnFile = queryNew("payProfileID,customerProfileID,paymentProfileID,otherFields,surchargeEligible","integer,varchar,varchar,varchar,boolean");
								if (queryAddRow(local.qryInfoOnFile)) {
									QuerySetCell(local.qryInfoOnFile,"payProfileID",local.qryInvoices.memberPaymentProfileID);
									QuerySetCell(local.qryInfoOnFile,"customerProfileID",local.qryInvoices.customerProfileID);
									QuerySetCell(local.qryInfoOnFile,"paymentProfileID",local.qryInvoices.paymentProfileID);
									QuerySetCell(local.qryInfoOnFile,"otherFields",local.qryInvoices.otherFields);
									QuerySetCell(local.qryInfoOnFile,"surchargeEligible",val(local.qryInvoices.surchargeEligible));
								}
							}
							</cfscript>

							<!--- Processing Fees --->
							<cfset local.additionalPaymentFees = 0>
							<cfset local.qryAdditionalFees = queryNew("additionalFees,additionalFeesExcTax,additionalFeesTax,additionalFeesRevTransDesc,additionalFeesLabel,stateIDForTax,zipForTax",
																"decimal,decimal,decimal,varchar,varchar,numeric,string")>

							<cfquery name="local.qryThisItemHasAdditionalPaymentFees" dbtype="query">
								SELECT DISTINCT invoiceID
								FROM [local].qryInvoices
								WHERE itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryInvoices.itemID#">
								AND additionalPaymentFee > 0
							</cfquery>

							<cfif local.qryThisItemHasAdditionalPaymentFees.recordCount>
								<cfset local.totalAmountToCharge = 0>
								<!--- loop per invoice --->
								<cfoutput>
									<cfif val(local.qryInvoices.additionalPaymentFee)>
										<cfset var strAdditionalPaymentFees = application.objPayments.getPaymentProcessingFees(
												gl = local.qryInvoices.paymentFeeTypeID EQ 1 ? local.qryInvoices.processFeeRevGlAccountID : local.qryInvoices.surchargeRevenueGLAccountID, 
												amt = local.qryInvoices.InvDueAmount, 
												feepct = local.qryInvoices.paymentFeeTypeID EQ 1 ? local.qryInvoices.processFeePercent : local.qryInvoices.surchargePercent, 
												stateIDForTax = val(local.qryInvoices.stateIDForTax), zipForTax = local.qryInvoices.zipForTax)>
										
										<cfset local.thisInvAdditionalPaymentFee = strAdditionalPaymentFees.processingfees>
										<cfset local.additionalPaymentFees = NumberFormat((local.additionalPaymentFees + local.thisInvAdditionalPaymentFee),'0.00')>
										<cfset local.totalAmountToCharge = NumberFormat((local.totalAmountToCharge + local.qryInvoices.InvDueAmount + local.thisInvAdditionalPaymentFee),'0.00')>

										<cfscript>
											if (queryAddRow(local.qryAdditionalFees)) {
												QuerySetCell(local.qryAdditionalFees,"additionalFees",local.thisInvAdditionalPaymentFee);
												QuerySetCell(local.qryAdditionalFees,"additionalFeesExcTax",strAdditionalPaymentFees.processingfeesExcTax);
												QuerySetCell(local.qryAdditionalFees,"additionalFeesTax",strAdditionalPaymentFees.tax);
												QuerySetCell(local.qryAdditionalFees,"additionalFeesRevTransDesc", local.qryInvoices.paymentFeeTypeID EQ 1 ? local.qryInvoices.processFeeRevTransDesc : 'Surcharge');
												QuerySetCell(local.qryAdditionalFees,"additionalFeesLabel",local.qryInvoices.additionalFeeLabel);
												QuerySetCell(local.qryAdditionalFees,"stateIDForTax",val(local.qryInvoices.stateIDForTax));
												QuerySetCell(local.qryAdditionalFees,"zipForTax",local.qryInvoices.zipForTax);
											}
										</cfscript>

										<!--- update processing fees --->
										<cfquery name="local.qryUpdateInvProcessingFees" datasource="#application.dsn.platformQueue.dsn#">
											UPDATE dbo.queue_payInvoicesDetail
											SET additionalPaymentFee = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#local.thisInvAdditionalPaymentFee#">
											where itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryInvoices.itemID#">
											AND invoiceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryInvoices.invoiceID#">
										</cfquery>
									<cfelse>
										<cfset local.totalAmountToCharge = NumberFormat((local.totalAmountToCharge + local.qryInvoices.InvDueAmount),'0.00')>
									</cfif>
								</cfoutput>

								<!--- update total payment amount after re-calculating processing fees --->
								<cfquery name="local.qryUpdatePaymentAmt" datasource="#application.dsn.platformQueue.dsn#">
									UPDATE dbo.queue_payInvoices
									SET paymentAmount = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#local.totalAmountToCharge#">
									where itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryInvoices.itemID#">
								</cfquery>
							<cfelse>
								<cfset local.totalAmountToCharge = local.qryInvoices.paymentAmount>
							</cfif>

							<cfset local.strTemp = { orgID=local.qryInvoices.orgid, siteid=local.qryInvoices.siteid, adminForm=1, 
													 profileCode=local.qryInvoices.profileCode, assignedToMemberID=local.qryInvoices.memberID, 
													 recordedByMemberID=local.MCSystemMemberID, statsSessionID=val(session.cfcuser.statsSessionID), 
													 x_amount=local.totalAmountToCharge, x_description='#local.qryInvoices.sitename# Payment', 
													 qryInfoOnFile=local.qryInfoOnFile, offeredPaymentFee=local.qryInvoices.paymentFeeTypeID EQ 1 }>
							<cfif listFindNoCase("AuthorizeCCCIM",local.qryInvoices.gatewayType)>
								<cfset local.qryLevel3Data = QueryNew("name,desc,itemPriceExcDiscount,itemPriceIncDiscount,discount,qty,total","varchar,varchar,decimal,decimal,decimal,decimal,decimal")>
								
								<!--- loop per invoice --->
								<cfoutput>
									<cfset QueryAddRow(local.qryLevel3Data, {
										"name": "Invoice #local.qryInvoices.invoiceNumber#",
										"desc": "#local.qryInvoices.sitename# Invoice #local.qryInvoices.invoiceNumber#",
										"itemPriceExcDiscount": local.qryInvoices.InvDueAmount,
										"itemPriceIncDiscount": local.qryInvoices.InvDueAmount,
										"discount": 0,
										"qty": 1,
										"total": local.qryInvoices.InvDueAmount
									})>
								</cfoutput>

								<cfif val(local.additionalPaymentFees)>
									<cfset QueryAddRow(local.qryLevel3Data, {
										"name": "#local.qryInvoices.additionalFeeLabel#",
										"desc": "#local.qryInvoices.sitename# #local.qryInvoices.additionalFeeLabel#",
										"itemPriceExcDiscount": local.additionalPaymentFees,
										"itemPriceIncDiscount": local.additionalPaymentFees,
										"discount": 0,
										"qty": 1,
										"total": local.additionalPaymentFees
									})>
								</cfif>
								<cfset local.strTemp["x_items"] = application.objPayments.getLevel3Data(qryLevel3Data=local.qryLevel3Data, gatewayType=local.qryInvoices.gatewayType)>
							</cfif>
							<cfinvoke component="#application.objPayments#" method="chargeAdHoc" argumentcollection="#local.strTemp#" returnvariable="local.paymentResponse">
				
							<cfquery name="local.updateItemDetails" datasource="#application.dsn.platformQueue.dsn#">
								update dbo.queue_payInvoices
								set paymentHistoryID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(local.paymentResponse.historyID)#">
								where itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryInvoices.itemID#">
							</cfquery>
	
							<!--- if not successful --->
							<cfif local.paymentResponse.responseCode is not 1>
								<cfset local.errCode = 1>
								<cfthrow message="Payment processing was not successful.<br/>#local.paymentResponse.responseReasonText#<br/>">
							</cfif>
	
							<cfquery name="local.updateItemDetails" datasource="#application.dsn.platformQueue.dsn#">
								update dbo.queue_payInvoices
								set paymentBatchID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.paymentResponse.mc_batchID#">,
									paymentTransactionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.paymentResponse.mc_transactionID#">
								where itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryInvoices.itemID#">
							</cfquery>

							<cfquery name="local.qryUpdateToProcessingAllocation" datasource="#application.dsn.platformQueue.dsn#">
								SET NOCOUNT ON;

								DECLARE @statusProcessing int;
								EXEC dbo.queue_getStatusIDbyType @queueType='PayInvoices', @queueStatus='processingAllocation', @queueStatusID=@statusProcessing OUTPUT;

								UPDATE dbo.queue_payInvoices
								SET statusID = @statusProcessing,
									dateUpdated = getdate()
								WHERE itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryInvoices.itemID#">;
							</cfquery>

							<!--- Record Surcharge / Processing Fee Donation --->
							<cfif local.qryAdditionalFees.recordCount>
								<cfset local.strRecordAdditionalPmtFees = local.objAccounting.recordAdditionalPaymentFees(orgID=local.qryInvoices.orgid, siteID=local.qryInvoices.siteid, 
									assignedToMemberID=local.qryInvoices.memberID, recordedByMemberID=local.MCSystemMemberID, 
									statsSessionID=val(session.cfcuser.statsSessionID), paymentTransactionID=local.paymentResponse.mc_transactionID,
									GLAccountID=local.qryInvoices.paymentFeeTypeID EQ 1 ? local.qryInvoices.processFeeRevGlAccountID : local.qryInvoices.surchargeRevenueGLAccountID, 
									qryAdditionalFees=local.qryAdditionalFees, paymentFeeTypeID=local.qryInvoices.paymentFeeTypeID)>
								
								<!--- if not successful --->
								<cfif NOT local.strRecordAdditionalPmtFees.success>
									<cfset local.errCode = 1>
									<cfthrow message="Recording Payment processing fees was not successful.<br/>#local.strRecordAdditionalPmtFees.errmsg#<br/>">
								</cfif>
							</cfif>
	
							<!--- loop per invoice under payment --->
							<cfoutput>
							
								<!--- ALLOCATE TO INVOICE --->
								<cfset local.allocationError = "">
								<cftry>
									<cfstoredproc procedure="tr_allocateToInvoice" datasource="#application.dsn.membercentral.dsn#">
										<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.qryInvoices.siteID#">
										<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.MCSystemMemberID#">
										<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#val(session.cfcuser.statsSessionID)#">
										<cfprocparam type="In" cfsqltype="CF_SQL_DECIMAL" scale="2" value="#local.qryInvoices.InvDueAmount#">
										<cfprocparam type="In" cfsqltype="CF_SQL_TIMESTAMP" value="#now()#">
										<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.paymentResponse.mc_transactionID#">
										<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.qryInvoices.invoiceID#">
									</cfstoredproc>
								<cfcatch type="Any">
									<cfset local.allocationError = local.allocationError & "Unable to allocate the payment to invoice #local.qryInvoices.invoiceNumber#.<br/>You will need to manually allocate this payment to the invoice in Control Panel.<br/>">

									<!--- send exception so we can see what the real issue is when unable to allocate --->
									<cfset local.strAllocError = { inv=local.qryInvoices.invoiceNumber, amt=local.qryInvoices.InvDueAmount, txnid=local.paymentResponse.mc_transactionID }>
									<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local.strAllocError)>
								</cfcatch>
								</cftry>
								<cfif len(local.allocationError)>
									<cfset local.errCode = 4>
									<cfthrow message="#local.allocationError#">
								</cfif>
							
							</cfoutput>
	
						</cfif>
		
					<cfcatch type="Any">
						<cfset local.errMsg = cfcatch.message>
						<cfif local.errCode is 4>
							<cfset local.errMsg = local.errMsg & "<i>Payment was successful - DO NOT process payment again!</i><br/>">
							<cfset local.errMsg = local.errMsg & "<i>Recording the payment in the A/R system was successful.</i><br/>">
						</cfif>				
						
						<cfquery name="local.updateItemDetails" datasource="#application.dsn.platformQueue.dsn#">
							update dbo.queue_payInvoices
							set paymentErrorMessage = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#local.errMsg#">
							where itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryInvoices.itemID#">
						</cfquery>
					</cfcatch>
					</cftry>

					<cfquery name="local.qryUpdateStatus" datasource="#application.dsn.platformQueue.dsn#">
						SET NOCOUNT ON;

						DECLARE @statusProcessing int;
						EXEC dbo.queue_getStatusIDbyType @queueType='PayInvoices', @queueStatus='readyToNotify', @queueStatusID=@statusProcessing OUTPUT;

						UPDATE dbo.queue_payInvoices
						SET statusID = @statusProcessing,
							dateUpdated = getdate()
						WHERE itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryInvoices.itemID#">;
					</cfquery>

				</cfif>
				
			</cfoutput>

			<!--- ----------------------------------------------------------------------------------- --->
			<!--- post processing: process any notifications for itemGroupUIDs that are readyToNotify --->
			<!--- ----------------------------------------------------------------------------------- --->
			<cftry>
				<cfstoredproc procedure="queue_payInvoices_grabForNotification" datasource="#application.dsn.platformQueue.dsn#">
					<cfprocresult name="local.qryNotifications" resultset="1">
				</cfstoredproc>
				<cfif local.qryNotifications.recordcount>
					<cfset local.tdStyle = "font-family:Verdana, Arial, Helvetica, sans-serif;font-size:9pt;color:##333;padding:0;">
					<cfset local.tdStyle2 = "font-family:Verdana, Arial, Helvetica, sans-serif;font-size:9pt;color:##999;padding:0 0 4px 20px;">
					<cfset local.thStyle = "font-family:Verdana, Arial, Helvetica, sans-serif;font-size:9pt;color:##333;border-bottom:1px solid ##333;padding:0;">
					<cfset local.pageStyle = "width:600px;padding:1px 1px 1px 1px;font-family:Verdana, Arial, Helvetica, sans-serif;font-size:9pt;color:##333;">
					<cfset local.errStyle = "color:##f00;font-weight:bold;">
					<cfset local.sucStyle = "color:##393;font-weight:bold;">
	
					<cfoutput query="local.qryNotifications" group="itemGroupUID">
						<cftry>
							<cfset local.thisAccountingEmail = local.qryNotifications.accountingEmail>
							<cfset local.thisOrgName = local.qryNotifications.orgName>
							<cfset local.thisSiteID = local.qryNotifications.siteID>
							<cfset local.thisOrgID = local.qryNotifications.orgID>
							<cfset local.mc_siteinfo = application.objSiteInfo.getSiteInfo(local.qryNotifications.sitecode)>

							<cfif len(local.thisAccountingEmail)>

								<!--- get members with issues --->
								<cfquery name="local.qryMembersWithIssues" dbtype="query">
									select distinct memberID
									from [local].qryNotifications
									where itemGroupUID = '#local.qryNotifications.itemGroupUID#'
									and paymentErrorMessage <> ''
								</cfquery>
								<cfif local.qryMembersWithIssues.recordcount>
									<cfquery name="local.qryIPReportFieldSet" datasource="#application.dsn.membercentral.dsn#">
										SET NOCOUNT ON;
										SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
										
										SELECT TOP 1 mfu.fieldsetID
										FROM dbo.ams_memberFieldUsage as mfu
										inner join dbo.cms_siteResources as sr on sr.siteResourceID = mfu.siteResourceID
										inner join dbo.cms_siteResourceTypes as srt on srt.resourceTypeID = sr.resourceTypeID 
										where srt.resourceType = 'AccountingAdmin'
										and sr.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisSiteID#">
										and sr.siteResourceStatusID = 1
										and mfu.area = 'ipreport'
										order by mfu.fieldsetorder, mfu.fieldsetID;

										SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
									</cfquery>
									<cfset local.ipReportFieldsetID = local.qryIPReportFieldSet.fieldsetID>

									<cfquery name="local.qryMemberDataIssues" datasource="#application.dsn.membercentral.dsn#">
										SET XACT_ABORT, NOCOUNT ON;
										BEGIN TRY

											SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

											IF OBJECT_ID('tempdb..##tmpMembers') IS NOT NULL 
												DROP TABLE ##tmpMembers;
											IF OBJECT_ID('tempdb..##tmpMembersFS') IS NOT NULL
												DROP TABLE ##tmpMembersFS;
											CREATE TABLE ##tmpMembers (memberID int PRIMARY KEY, lastname varchar(75), firstname varchar(75), 
												membernumber varchar(50), company varchar(200), row int);
											CREATE TABLE ##tmpMembersFS (MFSAutoID int IDENTITY(1,1) NOT NULL);

											declare @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisOrgID#">,
												@outputFieldsXML xml;

											insert into ##tmpMembers (memberID, lastname, firstname, membernumber, company, row)
											select mActive.memberID, mActive.lastname, mActive.firstname, mActive.memberNumber, mActive.company,
												ROW_NUMBER() OVER (ORDER BY mActive.lastname, mActive.firstname, mActive.memberNumber)
											from dbo.ams_members as m
											inner join dbo.ams_members as mActive on mActive.orgID = @orgID
												and mActive.memberID = m.activeMemberID
											where m.orgID = @orgID
											and m.memberid in (#ValueList(local.qryMembersWithIssues.memberid)#);

											-- get fieldset data and set back to snapshot because proc ends in read committed
											EXEC dbo.ams_getMemberDataByFieldSets @orgID=@orgID, 
												@fieldsetIDList=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.ipReportFieldsetID#">,
												@existingFields='m_lastname,m_firstname,m_membernumber,m_company', @ovNameFormat=NULL, @ovMaskEmails=0, 
												@membersTableName='##tmpMembers', @membersResultTableName='##tmpMembersFS', @linkedMembers=0, 
												@mode='view', @outputFieldsXML=@outputFieldsXML OUTPUT;

											SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

											select tmp.row, tmp.lastname, tmp.firstname, tmp.memberNumber, tmp.company, m.*,
												case when tmp.row = 1 then @outputFieldsXML else null end AS mc_outputFieldsXML
											from ##tmpMembers as tmp
											inner join ##tmpMembersFS as m on m.memberID = tmp.memberID
											order by tmp.row;

											SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

										END TRY
										BEGIN CATCH
											IF @@trancount > 0 ROLLBACK TRANSACTION;
											SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
											EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
										END CATCH
									</cfquery>

									<cfif local.qryMemberDataIssues.recordcount>
										<cfset local.xmlResultFields = XmlParse(local.qryMemberDataIssues.mc_outputFieldsXML[1])>
										<cfset local.qryOutputFields = CreateObject("component","model.system.platform.memberFieldsets").getOutputFieldsFromXML(outputFieldsXML=local.xmlResultFields)>

										<cfset local.orgAddressTypes = application.objOrgInfo.getOrgAddressTypes(orgID=local.thisOrgID, includeTags=1)>
										<cfset local.strOrgAddressTypes = structNew()>
										<cfloop query="local.orgAddressTypes">
											<cfif local.orgAddressTypes.isTag is 1>
												<cfset local.strOrgAddressTypes["t#local.orgAddressTypes.addressTypeID#"] = local.orgAddressTypes.addressType>
											<cfelse>
												<cfset local.strOrgAddressTypes[local.orgAddressTypes.addressTypeID] = local.orgAddressTypes.addressType>
											</cfif>
										</cfloop>

										<!--- combine address fields if there are any --->
										<cfset local.mc_combinedAddresses = structNew()>
										<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[substring(@fieldCode,1,3)='ma_' or substring(@fieldCode,1,3)='mp_' or substring(@fieldCode,1,4)='mat_' or substring(@fieldCode,1,4)='mpt_']")>
										<cfloop array="#local.tmp#" index="local.thisField">
											<cfset local.thisATID = listGetAt(local.thisField.xmlattributes.fieldcode,2,"_")>
											<cfif listFindNoCase("mat,mpt",listGetAt(local.thisField.xmlattributes.fieldcode,1,"_"))>
												<cfset local.strKey = "t#local.thisATID#">
											<cfelse>
												<cfset local.strKey = local.thisATID>
											</cfif>
											<cfif NOT StructKeyExists(local.mc_combinedAddresses,local.strKey)>
												<cfset local.mc_combinedAddresses[local.strKey] = { addr='', type=local.strOrgAddressTypes[local.strKey] } >
											</cfif>
										</cfloop>

										<cfset local.RecordTypeInFS = XMLSearch(local.xmlResultFields,"//field[@fieldCode='m_recordtypeid']")>
										<cfset local.memberTypeInFS = XMLSearch(local.xmlResultFields,"//field[@fieldCode='m_membertypeid']")>
										<cfset local.memberStatusInFS = XMLSearch(local.xmlResultFields,"//field[@fieldCode='m_status']")>

										<!--- remove fields from qryOutputFields that are handled manually --->
										<cfquery name="local.qryOutputFieldsForLoop" dbtype="query">
											SELECT *
											FROM [local].qryOutputFields
											WHERE fieldcodeSect NOT IN ('mc','m','ma','mat','mp','mpt')									
										</cfquery>
									</cfif>
								</cfif>

								<cfsavecontent variable="local.thisEmailContent">
									<cfif local.qryNotifications.isSystemGenerated is 1>
										<p>We have completed processing the following scheduled invoice payments. 
									<cfelse>
										<p>We have completed processing the following submitted invoice payments.
									</cfif>
									You should review this list for any issues.</p>

									<table style="width:600px;">
									<tr>
										<td style="#local.thStyle#" colspan="2"><b>Member</b></td>
										<td style="#local.thStyle#padding-left:4px;text-align:right;"><b>Pay&nbsp;Date</b></td>
										<td style="#local.thStyle#padding-left:4px;text-align:right;"><b>Result</b></td>
									</tr>
									
									<cfset local.rowNumber = 0>
									<cfoutput group="itemID">
										<cfset local.rowNumber = local.rowNumber + 1>
										<tr valign="top">
											<td style="#local.tdStyle#width:18px;" nowrap>#local.rowNumber#.</td>
											<td style="#local.tdStyle#">
												<cfif len(local.qryNotifications.paymentErrorMessage)>
													<cfquery name="local.qryMemberData" dbtype="query">
														select *
														from [local].qryMemberDataIssues
														where memberID = #local.qryNotifications.memberID#
													</cfquery>

													<!---Required CurrentRow---->
													<cfset local.mc_combinedName = local.qryMemberData['Extended MemberNumber']> 

													<!--- combine address fields if there are any --->
													<cfset local.thisMem_mc_combinedAddresses = duplicate(local.mc_combinedAddresses)>
													<cfloop collection="#local.thisMem_mc_combinedAddresses#" item="local.thisATID">
														<cfsavecontent variable="local.thisATFull">
															<cfoutput>
															<cfif left(local.thisATID,1) eq "t">
																<cfset local.MAfcPrefix = "mat_#right(local.thisATID,len(local.thisATID)-1)#_">
																<cfset local.MPfcPrefix = "mpt_#right(local.thisATID,len(local.thisATID)-1)#_">
															<cfelse>
																<cfset local.MAfcPrefix = "ma_#local.thisATID#_">
																<cfset local.MPfcPrefix = "mp_#local.thisATID#_">
															</cfif>
															<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#address1']")>
															<cfif arrayLen(local.tmp) is 1 and len(local.qryMemberData[local.tmp[1].xmlAttributes.FieldLabel][local.qryMemberData.currentrow])>#local.qryMemberData[local.tmp[1].xmlAttributes.FieldLabel][local.qryMemberData.currentrow]#<br/> </cfif>
															<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#address2']")>
															<cfif arrayLen(local.tmp) is 1 and len(local.qryMemberData[local.tmp[1].xmlAttributes.FieldLabel][local.qryMemberData.currentrow])>#local.qryMemberData[local.tmp[1].xmlAttributes.FieldLabel][local.qryMemberData.currentrow]#<br/> </cfif>
															<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#address3']")>
															<cfif arrayLen(local.tmp) is 1 and len(local.qryMemberData[local.tmp[1].xmlAttributes.FieldLabel][local.qryMemberData.currentrow])>#local.qryMemberData[local.tmp[1].xmlAttributes.FieldLabel][local.qryMemberData.currentrow]#<br/></cfif>
															<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#city']")>
															<cfif arrayLen(local.tmp) is 1 and len(local.qryMemberData[local.tmp[1].xmlAttributes.FieldLabel][local.qryMemberData.currentrow])>#local.qryMemberData[local.tmp[1].xmlAttributes.FieldLabel][local.qryMemberData.currentrow]# </cfif>
															<cfset local.tmp2 = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#stateprov']")>
															<cfif arrayLen(local.tmp2) is 1 and len(local.qryMemberData[local.tmp2[1].xmlAttributes.FieldLabel][local.qryMemberData.currentrow])>, #local.qryMemberData[local.tmp2[1].xmlAttributes.FieldLabel][local.qryMemberData.currentrow]# </cfif>
															<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#postalcode']")>
															<cfif arrayLen(local.tmp) is 1 and len(local.qryMemberData[local.tmp[1].xmlAttributes.FieldLabel][local.qryMemberData.currentrow])> #local.qryMemberData[local.tmp[1].xmlAttributes.FieldLabel][local.qryMemberData.currentrow]#<br/></cfif>
															<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#county']")>
															<cfif arrayLen(local.tmp) is 1 and len(local.qryMemberData[local.tmp[1].xmlAttributes.FieldLabel][local.qryMemberData.currentrow])>#local.qryMemberData[local.tmp[1].xmlAttributes.FieldLabel][local.qryMemberData.currentrow]# County<br/></cfif>
															<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#country']")>
															<cfif arrayLen(local.tmp) is 1 and len(local.qryMemberData[local.tmp[1].xmlAttributes.FieldLabel][local.qryMemberData.currentrow])> #local.qryMemberData[local.tmp[1].xmlAttributes.FieldLabel][local.qryMemberData.currentrow]#<br/></cfif>
															<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[substring(@fieldCode,1,#len(local.MPfcPrefix)#)='#local.MPfcPrefix#']")>
															<cfloop array="#local.tmp#" index="local.thisPT">
																<cfif len(local.qryMemberData[local.thisPT.xmlAttributes.FieldLabel][local.qryMemberData.currentrow])>
																	<div>#local.thisPT.xmlAttributes.FieldLabel#: #local.qryMemberData[local.thisPT.xmlAttributes.FieldLabel][local.qryMemberData.currentrow]#</div>
																</cfif>
															</cfloop>
															</cfoutput>
														</cfsavecontent>
														<cfset local.thisATfull = trim(replace(replace(local.thisATFull,'  ',' ','ALL'),' ,',',','ALL'))>
														<cfif left(local.thisATfull,2) eq ", ">
															<cfset local.thisATfull = right(local.thisATfull,len(local.thisATfull)-2)>
														</cfif>
														<cfif len(local.thisATfull)>
															<cfset local.thisMem_mc_combinedAddresses[local.thisATID]['addr'] = local.thisATfull>
														<cfelse>
															<cfset structDelete(local.thisMem_mc_combinedAddresses,local.thisATID,false)>
														</cfif>
													</cfloop>

													<!--- get recordtype if available --->
													<cfif arrayLen(local.RecordTypeInFS) is 1 and len(local.qryMemberData[local.RecordTypeInFS[1].xmlAttributes.FieldLabel][1])>
														<cfset local.mc_recordType = local.qryMemberData[local.RecordTypeInFS[1].xmlAttributes.FieldLabel][1]>
													<cfelse>
														<cfset local.mc_recordType = "">
													</cfif>
													
													<!--- get membertypeid if available --->
													<cfif arrayLen(local.memberTypeInFS) is 1 and len(local.qryMemberData[local.memberTypeInFS[1].xmlAttributes.FieldLabel][1])>
														<cfset local.mc_memberType = local.qryMemberData[local.memberTypeInFS[1].xmlAttributes.FieldLabel][1]>
													<cfelse>
														<cfset local.mc_memberType = "">
													</cfif>	
													
													<!--- get status if available --->
													<cfif arrayLen(local.memberStatusInFS) is 1 and len(local.qryMemberData[local.memberStatusInFS[1].xmlAttributes.FieldLabel][1])>
														<cfset local.mc_memberStatus = local.qryMemberData[local.memberStatusInFS[1].xmlAttributes.FieldLabel][1]>
													<cfelse>
														<cfset local.mc_memberStatus = "">
													</cfif>	

													<a href="#local.mc_siteInfo.scheme#://#local.mc_siteinfo.mainhostname#/?pg=admin&mca_s=3&mca_a=19&mca_tt=10&mca_ta=edit&memberID=#local.qryNotifications.memberID#&tab=transactions">#local.mc_combinedName#</a><br/>
													<cfif len(local.qryMemberData.company)>
														<div>#local.qryMemberData.company#</div>
													</cfif>
													<cfif len(local.mc_recordType)>
														<div>#local.mc_recordType#</div>
													</cfif>
													<cfif len(local.mc_memberType)>
														<div>#local.mc_memberType#</div>
													</cfif>
													<cfif len(local.mc_memberStatus)>
														<div>#local.mc_memberStatus#</div>
													</cfif>
													<cfif StructCount(local.thisMem_mc_combinedAddresses)>
														<cfloop collection="#local.thisMem_mc_combinedAddresses#" item="local.thisATID">
															<div>#local.thisMem_mc_combinedAddresses[local.thisATID]['type']#:<br/>#local.thisMem_mc_combinedAddresses[local.thisATID]['addr']#</div>
														</cfloop>
													</cfif>

													<cfif local.qryOutputFieldsForLoop.recordCount>
														<cfloop query="local.qryOutputFieldsForLoop">
															<cfset local.currValue = local.qryMemberData[local.qryOutputFieldsForLoop.fieldLabel][local.qryMemberData.currentrow]>
															<cfif len(local.currValue)>
																<div style="white-space:nowrap;overflow:hidden;text-overflow: ellipsis;">
																	#htmlEditFormat(local.qryOutputFieldsForLoop.fieldLabel)#: 
																	<cfif left(local.qryOutputFieldsForLoop.fieldCode,13) eq 'acct_balance_'>
																		#dollarFormat(local.currValue)#
																	<cfelse>
																		<cfswitch expression="#local.qryOutputFieldsForLoop.dataTypeCode#">
																			<cfcase value="DATE">
																				#dateFormat(local.currValue,"m/d/yyyy")#
																			</cfcase>
																			<cfcase value="STRING,DECIMAL2,INTEGER">
																				#local.currValue#
																			</cfcase>
																			<cfcase value="BIT">
																				#YesNoFormat(local.currValue)#
																			</cfcase>
																		</cfswitch>
																	</cfif>
																</div>
															</cfif>
														</cfloop>
													</cfif>
												<cfelse>
													<a href="#local.mc_siteInfo.scheme#://#local.mc_siteinfo.mainhostname#/?pg=admin&mca_s=3&mca_a=19&mca_tt=10&mca_ta=edit&memberID=#local.qryNotifications.memberID#&tab=transactions">#local.qryNotifications.memberName# (#local.qryNotifications.membernumber#)</a><br/>
													#local.qryNotifications.company#
												</cfif>
											</td>
											<td style="#local.tdStyle#text-align:right;padding-left:4px;">#dateFormat(local.qryNotifications.datePaid,'m/d/yyyy')#</td>
											<td style="#local.tdStyle#text-align:right;padding-left:4px;">
												<cfif len(local.qryNotifications.paymentErrorMessage)>
													<div style="#local.errStyle#">Issue</div>
												<cfelse>
													<div style="#local.sucStyle#">Success</div>
												</cfif>
											</td>
										</tr>
										<tr valign="top">
											<td></td>
											<td colspan="3" style="#local.tdStyle2#">
												#dollarformat(local.qryNotifications.paymentAmount)# Payment by #local.qryNotifications.payMethodDetail# via #local.qryNotifications.payProfileName#
												<cfoutput>
													<br/>- #dollarformat(local.qryNotifications.invoiceDueAmount)# for Invoice #local.qryNotifications.invoiceNumber# (#local.qryNotifications.invoiceProfileName#) - Due #dateformat(local.qryNotifications.dateDue,"m/d/yyyy")#
													<cfif val(local.qryNotifications.additionalPaymentFee)>
														<br/>- #dollarformat(local.qryNotifications.additionalPaymentFee)# #local.qryNotifications.additionalFeeLabel# for Invoice #local.qryNotifications.invoiceNumber# (#local.qryNotifications.invoiceProfileName#)
													</cfif>
												</cfoutput>
												<cfif len(local.qryNotifications.paymentErrorMessage)>
													<div style="#local.errStyle#">#local.qryNotifications.paymentErrorMessage#</div>
												</cfif>
											</td>
										</tr>
									</cfoutput>
									
									</table>
									<br/>
									<div><b>Questions?</b><br/>Contact <NAME_EMAIL> if you have any questions about this report.</div>
									<cfif local.qryNotifications.isSystemGenerated is 1>
										<br/>
										<div><b>E-mail Removal</b><br/>If you no longer wish to receive these types of emails, you can adjust the 
										recipient list by logging into your website's Control Panel, clicking on the Accounting tab, and modifying this 
										report's recipient address in the "Accounting Settings" tool.</div>
									</cfif>
								</cfsavecontent>
	
								<cfset local.emailTitle = "#local.thisOrgName# Invoice Payment Report">
								<cfscript>
									local.arrEmailTo = [];
									
									local.toEmailArr = listToArray(local.thisAccountingEmail,';');
									for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
										local.arrEmailTo.append({ name:'', email:local.toEmailArr[local.i] });
									}
								</cfscript>
								<cfset local.strEmailResult = application.objEmailWrapper.sendMailESQ(
									emailfrom={ name='MemberCentral', email='<EMAIL>' },
									emailto=local.arrEmailTo,
									emailreplyto="<EMAIL>",
									emailsubject="Invoice Payment Report for #local.thisOrgName#",
									emailtitle=local.emailTitle,
									emailhtmlcontent=local.thisEmailContent,
									emailAttachments=[],
									siteID=local.mc_siteinfo.siteID,
									memberID=local.qryNotifications.isSystemGenerated is 1 ? local.mc_siteinfo.sysMemberID : local.qryNotifications.recordedByMemberID,
									messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="SCHEDTASK"),
									sendingSiteResourceID=local.mc_siteinfo.siteSiteResourceID
								)>
							</cfif>
							
							<!--- ------------- --->
							<!--- UPDATE STATUS --->
							<!--- ------------- --->
							<cfquery name="local.updateStatusInMass" datasource="#application.dsn.platformQueue.dsn#">
								SET NOCOUNT ON;
		
								declare @newstatus int;
								select @newstatus = qs.queueStatusID 
									from dbo.tblQueueStatuses as qs
									inner join dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
									where qt.queueType = 'PayInvoices'
									and qs.queueStatus = 'done';

								UPDATE dbo.queue_payInvoices WITH (UPDLOCK, HOLDLOCK)
								SET statusID = @newstatus,
									dateUpdated = getdate()
								WHERE itemGroupUID = <cfqueryparam cfsqltype="CF_SQL_IDSTAMP" value="#local.qryNotifications.itemGroupUID#">;
							</cfquery>
	
						<cfcatch type="Any">
							<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
						</cfcatch>
						</cftry>
					</cfoutput>
	
				</cfif>
			<cfcatch type="Any">
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			</cfcatch>
			</cftry>

			<!--- -------------------------------------------------------- --->
			<!--- post processing - delete any itemGroupUIDs that are done --->
			<!--- -------------------------------------------------------- --->
			<cftry>
				<cfstoredproc procedure="queue_payInvoices_clearDone" datasource="#application.dsn.platformQueue.dsn#">
				</cfstoredproc>
			<cfcatch type="Any">
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			</cfcatch>
			</cftry>

		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset local.success = false>
		</cfcatch>
		</cftry>			
					
		<cfreturn local.success>
	</cffunction>

	<cffunction name="getQueueItemCount" access="private" output="false" returntype="numeric">
		<cfset var qryQueueItems = "">

		<cfquery name="qryQueueItems" datasource="#application.dsn.platformQueue.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select count(itemID) as itemCount
			from dbo.queue_payInvoices;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryQueueItems.itemCount>
	</cffunction>

</cfcomponent>