ALTER PROC dbo.queue_swlCertificate_grabForProcessing

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @batchSize int, @statusReady int, @statusGrabbed int;
	set @batchSize = 50;

	select @statusReady = qs.queueStatusID 
	from dbo.tblQueueStatuses as qs
	inner join dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
	where qt.queueType = 'swlCertificate'
	and qs.queueStatus = 'readyToProcess';

	select @statusGrabbed = qs.queueStatusID 
	from dbo.tblQueueStatuses as qs
	inner join dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
	where qt.queueType = 'swlCertificate'
	and qs.queueStatus = 'grabbedForProcessing';

	IF OBJECT_ID('tempdb..#tmpSWLCerts') IS NOT NULL 
		DROP TABLE #tmpSWLCerts;
	CREATE TABLE #tmpSWLCerts(itemID int);

	-- dequeue in order of dateAdded. get @batchsize payments
	update qi WITH (UPDLOCK, READPAST)
	set qi.statusID = @statusGrabbed,
		qi.dateUpdated = getdate()
		OUTPUT inserted.itemID INTO #tmpSWLCerts
	from dbo.queue_swlCertificate as qi
	inner join (
			select top(@BatchSize) qi2.itemID 
			from dbo.queue_swlCertificate as qi2
			where qi2.statusID = @statusReady
			order by qi2.dateAdded, qi2.itemID
		) as batch on batch.itemID = qi.itemID
	where qi.statusID = @statusReady;

	-- final data
	select qidd.itemID, qidd.itemGroupUID, qidd.performedByDepoMemberDataID, qidd.enrollmentID, qidd.isProcessed, qidd.errMessage, mrh.recipientID,mrh.messageID as emailMessageID,mrh.siteID as emailSiteID,
		p.orgCode as signUpOrgCode, e.seminarID, qidd.outgoingType, mrh.toEmail as email
	from #tmpSWLCerts as qid
	inner join dbo.queue_swlCertificate as qidd on qidd.itemID = qid.itemID
	inner join seminarWeb.dbo.tblEnrollments as e on e.enrollmentID = qidd.enrollmentID
	inner join seminarWeb.dbo.tblParticipants as p on p.participantID = e.participantID
	inner join platformMail.dbo.email_messageRecipientHistory as mrh on mrh.recipientID = qidd.recipientID
	order by qidd.itemID;

	IF OBJECT_ID('tempdb..#tmpSWLCerts') IS NOT NULL 
		DROP TABLE #tmpSWLCerts;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
END CATCH
GO
