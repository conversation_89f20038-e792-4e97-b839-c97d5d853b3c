ALTER PROC dbo.queue_taxjarorders_grabForProcessing
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @statusReady int, @statusGrabbed int;

	select @statusReady = qs.queueStatusID 
	from dbo.tblQueueStatuses as qs
	inner join dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
	where qt.queueType = 'taxjarorders'
	and qs.queueStatus = 'readyToProcess';

	select @statusGrabbed = qs.queueStatusID 
	from dbo.tblQueueStatuses as qs
	inner join dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
	where qt.queueType = 'taxjarorders'
	and qs.queueStatus = 'grabbedForProcessing';

	IF OBJECT_ID('tempdb..#tmpTblQueueItems_taxjarorders') IS NOT NULL 
		DROP TABLE #tmpTblQueueItems_taxjarorders;
	CREATE TABLE #tmpTblQueueItems_taxjarorders(itemID int);

	-- dequeue
	UPDATE qi WITH (UPDLOCK, READPAST)
	SET qi.statusID = @statusGrabbed,
		qi.dateUpdated = getdate()
		OUTPUT inserted.itemID INTO #tmpTblQueueItems_taxjarorders
	FROM dbo.queue_taxjarorders as qi
	INNER JOIN (
		SELECT top 1 qi2.itemID 
		FROM dbo.queue_taxjarorders as qi2
		WHERE qi2.statusID = @statusReady
		ORDER BY qi2.dateAdded, qi2.itemID
	) as batch on batch.itemID = qi.itemID
	WHERE qi.statusID = @statusReady;


	-- final order data
	select qid.itemID, qid.orgID, qid.apiKey, qid.transaction_id, qid.transaction_date, qid.to_state, qid.to_zip,
		(select sum(unit_price) from dbo.queue_taxjarordersDetail where itemID = qid.itemID) as amount,
		(select sum(sales_tax) from dbo.queue_taxjarordersDetail where itemID = qid.itemID) as sales_tax
	from #tmpTblQueueItems_taxjarorders as qi
	inner join dbo.queue_taxjarorders as qid on qid.itemID = qi.itemID;

	-- final order detail data
	select qid.product_tax_code, qid.unit_price, qid.sales_tax
	from #tmpTblQueueItems_taxjarorders as qi
	inner join dbo.queue_taxjarordersDetail as qid on qid.itemID = qi.itemID
	order by qid.product_tax_code;


	IF OBJECT_ID('tempdb..#tmpTblQueueItems_taxjarorders') IS NOT NULL 
		DROP TABLE #tmpTblQueueItems_taxjarorders;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
END CATCH
GO
