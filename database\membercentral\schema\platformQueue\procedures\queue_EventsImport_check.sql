ALTER PROC dbo.queue_EventsImport_check

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @issueCount INT, @timeToUse DATETIME, @queueTypeID INT, @queueStatusID INT, @nextQueueStatusID INT, 
		@itemAsStr VARCHAR(60), @xmlMessage xml, @errorSubject VARCHAR(400), @errorTitle VARCHAR(400);
	SELECT @queueTypeID = queueTypeID FROM dbo.tblQueueTypes WHERE queueType = 'importEvents';

	-- importEvents / processingEvent autoreset to readyToProcess
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -10, GETDATE());
	SELECT @queueStatusID = queuestatusID FROM dbo.tblQueueStatuses WHERE queueTypeID = @queueTypeID AND queueStatus = 'processingEvent';
	SELECT @nextQueueStatusID = queuestatusID FROM dbo.tblQueueStatuses WHERE queueTypeID = @queueTypeID AND queueStatus = 'readyToProcess';
	SELECT @issueCount = count(itemUID) FROM dbo.tblQueueItems WHERE queueStatusID = @queueStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.tblQueueItems
		SET queueStatusID = @nextQueueStatusID, 
			dateUpdated = GETDATE()
		WHERE queueStatusID = @queueStatusID 
		AND dateUpdated < @timeToUse;

		SET @errorTitle = 'importEvents Queue Issue';
		SET @errorSubject = 'importEvents queue moved items from processingEvent to readyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- importEvents / readyToProcess resend message
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -220, GETDATE());
	SELECT @queueStatusID = queuestatusID FROM dbo.tblQueueStatuses WHERE queueTypeID = @queueTypeID AND queueStatus = 'readyToProcess';
	SELECT @issueCount = count(itemUID) FROM dbo.tblQueueItems WHERE queueStatusID = @queueStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.tblQueueItems
		SET dateUpdated = GETDATE()
			OUTPUT inserted.itemUID 
			INTO #tmpCheckQueueMsgs (itemAsStr)
		WHERE queueStatusID = @queueStatusID 
		AND dateUpdated < @timeToUse;

		SELECT @itemAsStr = MIN(itemAsStr) FROM #tmpCheckQueueMsgs;
		WHILE @itemAsStr IS NOT NULL BEGIN
			SET @xmlMessage = '<mc i="' + @itemAsStr + '" />';
			EXEC dbo.queue_EventsImport_sendMessage @xmlMessage=@xmlMessage;
			SELECT @itemAsStr = MIN(itemAsStr) FROM #tmpCheckQueueMsgs WHERE itemAsStr > @itemAsStr;
		END

		TRUNCATE TABLE #tmpCheckQueueMsgs;
		
		SET @errorTitle = 'importEvents Queue Issue';
		SET @errorSubject = 'importEvents queue resent items in readyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- importEvents / readyToNotify or grabbedForNotifying with dateupdated older than 4 hours, something else in the group must be stopping it
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(hour, -4, GETDATE())
	SELECT @queueStatusID = queuestatusID FROM dbo.tblQueueStatuses WHERE queueTypeID = @queueTypeID AND queueStatus = 'readyToNotify';
	SELECT @nextQueueStatusID = queuestatusID FROM dbo.tblQueueStatuses WHERE queueTypeID = @queueTypeID AND queueStatus = 'grabbedForNotifying';
	SELECT @issueCount = count(itemUID) FROM dbo.tblQueueItems WHERE queueStatusID in (@queueStatusID,@nextQueueStatusID) AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		SET @errorTitle = 'importEvents Queue Issue';
		SET @errorSubject = 'importEvents queue has items in readyToNotify or grabbedForNotifying last updated more than 4 hours ago';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- importEvents catchall
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(hour, -4, GETDATE())
	SELECT @issueCount = count(qi.itemUID)
		FROM dbo.tblQueueItems AS qi 
		inner join dbo.tblQueueStatuses AS qs on qs.queueTypeID = @queueTypeID AND qs.queueStatusID = qi.queueStatusID 
		WHERE qi.dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		SET @errorTitle = 'importEvents Queue Issue';
		SET @errorSubject = 'importEvents queue has items last updated more than 4 hours ago';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
