<cfcomponent extends="model.admin.admin" output="no">
	<cfset defaultEvent = 'controller'>

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		<cfscript>
		var local = structNew();
		
		arguments.event.getCollection()['mc_admintoolInfo']['myRights'] = buildRightAssignments(this.siteResourceID,session.cfcuser.memberData.memberid,arguments.event.getValue('mc_siteinfo.siteid'));
		// build quick links -------------------------------------------------------------------------- ::
		this.link.list = buildCurrentLink(arguments.event,"list");
		this.link.edit = buildCurrentLink(arguments.event,"edit")& "&mode=direct";
		this.link.addPage = buildCurrentLink(arguments.event,"addPage")& "&mode=direct";
		this.link.copyPage = buildCurrentLink(arguments.event,"copyPage")& "&mode=direct";
		this.link.exportPage = buildCurrentLink(arguments.event,"exportPage")& "&mode=direct";
		this.link.addSection = buildCurrentLink(arguments.event,"addSection")& "&mode=direct";
		this.link.insertSection = buildCurrentLink(arguments.event,"insertSection");
		this.link.addContentPage = buildCurrentLink(arguments.event,"addContentPage");
		this.link.reassignContent = buildCurrentLink(arguments.event,"reassignContent")& "&mode=direct";
		this.link.assignContent = buildCurrentLink(arguments.event,"assignContent");
		this.link.save = buildCurrentLink(arguments.event,"save");
		this.link.message = buildCurrentLink(arguments.event,"message");
		this.link.contentDetails = buildCurrentLink(arguments.event,"contentDetails");
		this.link.addAppInstance = buildCurrentLink(arguments.event,"addAppInstance");
		this.link.exportCSV = buildCurrentLink(arguments.event,"exportCSV") & "&mode=stream";
		this.link.exportJSON = buildCurrentLink(arguments.event,"exportJSON") & "&mode=stream";
		this.link.pageStats = buildLinkToTool('SiteStatsAdmin','pageDashboard');
		this.link.editMetaData = buildCurrentLink(arguments.event,"edit") & "&pageMetaDataAction=editMetaData&lockTab=true";
		// method to run ------------------------------------------------------------------------------ ::
		local.methodToRun = this[arguments.event.getValue('mca_ta')];
		// pass the argument collection to the current method and execute it.
		return local.methodToRun(arguments.event);
		</cfscript>
	</cffunction>

	<cffunction name="list" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();

			local.security.canAddPage = checkRights(arguments.event,'AddPage');
			local.security.canAddAppInstance = checkRights(arguments.event,'AddAppInstance');
			local.security.canManageSections = checkRights(arguments.event,'ManageSections');

			if (local.security.canAddPage is not 1 and local.security.canAddAppInstance is not 1 and local.security.canManageSections is not 1)
				application.objCommon.redirect('#this.link.message#&errcode=noaccess');
			
			local.objPage = CreateObject("component","model.admin.pages.pages");
			// session management ---------------------------------------------------- ::
			local.pageFilter = local.objPage.getPagesFilter();

			local.qryPageTypes = local.objPage.getPageTypes(siteID=arguments.event.getValue('mc_siteInfo.siteID'),includeApplicationCreatedSections=local.pageFilter.listFilter.advancedMode);
			local.rootSectionSRID = local.objPage.getRootSectionID(siteID=arguments.event.getValue('mc_siteinfo.siteid'));
			arguments.event.paramValue('isSearch',0);			
			arguments.event.paramValue('advancedMode',0); 
			arguments.event.paramValue('ssRID',0);
			arguments.event.paramValue('kw','');
			arguments.event.paramValue('shNP',0);
			arguments.event.paramValue('cs','');
			arguments.event.paramValue('ce','');
			arguments.event.paramValue('ms','');
			arguments.event.paramValue('me','');
			arguments.event.paramValue('pt','');
			arguments.event.paramValue('ps','1');

			// build JSON LINKS --------------------------------------------------------------------------
			local.sectionListLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=pageJSON&meth=getSectionList&srID=#this.siteResourceID#&mode=stream";
			local.pageListLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=pageJSON&meth=getPagesList&mode=stream";
			

			// import tab
			if (application.objUser.isSuperUser(cfcuser=session.cfcuser)) {
				local.sampleImportTemplateLink = buildCurrentLink(arguments.event,"sampleImportTemplate") & "&mode=stream";
				local.preProcessImportLink = buildCurrentLink(arguments.event,"preProcessImport");
				if (structKeyExists(arguments,"impData")) local.impData = arguments.impData;
			}
		</cfscript>
		<cfset local.selectedSectionID = 0>
		<cfif val(arguments.event.getValue('rid',0))>
			<cfset local.selectedSectionID = local.objPage.getSectionIDFromPageResourceID(siteID=arguments.event.getValue('mc_siteinfo.siteid'),siteResourceID=#arguments.event.getValue('rid')#)>
		</cfif>
		<cfquery name="local.generateUID" datasource="#application.dsn.membercentral.dsn#">
			select newid() as itemGroupUID
		</cfquery>
		<cfset local.uploadJSONImportFilesLink = buildCurrentLink(arguments.event,"uploadJSONImportFile") & "&itemGroupUID=#local.generateUID.itemGroupUID#&mode=stream">

		<cfset local.qryReadyToProcessPageExportJSONJobsCount = getReadyToProcessPageExportJSONJobsCount(siteID=arguments.event.getValue('mc_siteinfo.siteID'))>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_pages.cfm">
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<!--- edit is based off of siteResourceID --->
	<cffunction name="edit" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();

			local.security.canAddPage = checkRights(arguments.event,'AddPage');
			local.security.canManageSections = checkRights(arguments.event,'ManageSections');

			local.rc = arguments.event.getCollection();
			local.objPage = CreateObject("component","model.admin.pages.pages");
			local.objSection = CreateObject("component","model.system.platform.section");
			local.objPageMetaData = CreateObject("component","model.admin.pages.pageMetaDataGrid");
			local.rID = arguments.event.getValue('rID',0);
			
			local.qryData = local.objPage.getData(arguments.event.getValue('mc_siteInfo.siteID'),arguments.event.getValue('rID'));
			
			local.pageFilter = local.objPage.getPagesFilter();
			if (local.pageFilter.listFilter.advancedMode eq 1)
				local.getSections = local.objSection.getRecursiveSectionsDeep(siteID=arguments.event.getValue('mc_siteInfo.siteID'));
			else
				local.getSections = local.objSection.getRecursiveSections(siteID=arguments.event.getValue('mc_siteInfo.siteID'));

			local.qryModes = getAvailableModes();
			local.qryLanguages = getAvailableLanguages();
			local.qryTemplates = getAvailableTemplates(arguments.event.getValue('mc_siteInfo.siteID'));
			local.qryMobileTemplates = getAvailableTemplates(arguments.event.getValue('mc_siteInfo.siteID'), true);
			local.qryStatuses = getAvailableResourceStatuses();
			local.qryMenus = getMenus(arguments.event.getValue('mc_siteInfo.siteID'));
			local.qryMissingLanguages = local.objPageMetaData.getMissingMetadataLanguages(arguments.event.getValue('rID'));

			local.featuredImageConfigID = 0;
			if (local.qryData.isPage) {
				local.featuredImageConfigID = val(local.qryData.qryPage.websitePageFeatureImageConfigID);
				local.controllingReferenceType = 'websitePage';
				local.referenceID = local.qryData.qryPage.pageID;
				local.referenceType = "websitePage";
				local.resourceTypeTitle = local.qryData.qryPage.pageName;
			} else if (local.qryData.isSection) {
				local.featuredImageConfigID = val(local.qryData.qrySection.websitePageSectionFeatureImageConfigID);
				local.controllingReferenceType = 'websitePageSection';
				local.referenceID = local.qryData.qrySection.sectionID;
				local.referenceType = "websitePageSection";
				local.resourceTypeTitle = local.qryData.qrySection.sectionName;
			}

			if (local.featuredImageConfigID GT 0) {
				local.loadFtdImgLink = "/?pg=admin&mca_ajaxlib=featuredImages&mca_ajaxfunc=renderFeaturedImageSelector&_fcrid=#arguments.event.getValue('mc_siteInfo.siteID')#&_fcrtype=#local.controllingReferenceType#&_frid=#local.referenceID#&_frtype=#local.referenceType#&_frestype=PageAdmin&_frestypetitle=#encodeForURL(local.resourceTypeTitle)#&_fsaveimghandler=loadPageFtdImg&mode=stream";
			}
			local.isFromCommunity = int(val(arguments.event.getValue('isFromCommunity',0)));

			local.formSubmit = "#this.link.save#&rID=#local.rID#";

			
			if (local.qryData.isPage)
				appendBreadCrumbs(arguments.event,{ link='', text=encodeForHTML(local.qryData.qryPage.pageName) });
			if (local.qryData.isSection)
				appendBreadCrumbs(arguments.event,{ link='', text=encodeForHTML(local.qryData.qrySection.sectionName) });
		</cfscript>
		
		<cfsavecontent variable="local.data">
			<cfinclude template="frm_edit.cfm">
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	<!--- Present form to copy page in flyout --->
	<cffunction name="copyPage" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.rID = arguments.event.getValue('rID')>
		<cfset local.doCopyPageLink = buildCurrentLink(arguments.event,"doCopyPage")& "&mode=stream">

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_copy_page.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="save" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			local.rc = arguments.event.getCollection();
			local.objPage = CreateObject("component","model.admin.pages.pages");
			local.objSection = CreateObject("component","model.system.platform.section");
			local.objPageMetaData = CreateObject("component","model.admin.pages.pageMetaDataGrid");
			local.qryData = local.objPage.getData(arguments.event.getValue('mc_siteInfo.siteID'),arguments.event.getValue('rID'));
			local.rootSectionSRID = local.objPage.getRootSectionID(siteID=arguments.event.getValue('mc_siteinfo.siteid'));
			local.rID = arguments.event.getValue('rID',0);
			
			try {
				if( local.qryData.isPage ){

					local.dateUnavailable = trim(replace(arguments.event.getValue('dateUnavailable',''),' - ',' '));
					local.strFrom = 'page';
					local.newSectionSRID = local.objSection.getSectionSiteResourceID(arguments.event.getValue('mc_siteinfo.siteid'),arguments.event.getValue('newSectionID'));
					if (len(local.dateUnavailable)) local.dateUnavailable = ParseDateTime(local.dateUnavailable);

					local.objPage.updatePage(
						siteID=arguments.event.getValue('mc_siteInfo.siteID'),
						pageID=local.qryData.qryPage.pageID,
						pageName=arguments.event.getValue('newPageName',''),
						sectionID=arguments.event.getValue('newSectionID'),
						ovModeID=arguments.event.getValue('newOvModeID'),
						ovTemplateID=arguments.event.getValue('newOvTemplateID'),
						ovTemplateIDMobile=arguments.event.getValue('newOvTemplateIDMobile'),
						inheritPlacements=arguments.event.getValue('inheritPlacements'),
						pageDirectives=arguments.event.getTrimValue('pageDirectives',''),
						dateUnavailable=local.dateUnavailable,
						siteResourceStatusID=arguments.event.getValue('siteResourceStatusID'));

					if(len(arguments.event.getValue('languageID',''))) {
						local.languageCount = ListLen(arguments.event.getValue('languageID'));
						for (local.i = 1; local.i lte local.languageCount; local.i++) {
							local.languageId = ListGetAt(arguments.event.getValue('languageID'), local.i);
							local.objPageMetaData.updatePageMetaData(
								pageId = local.qryData.qryPage.pageID
								,pageLanguageId = arguments.event.getValue('pageLanguageId_#local.languageId#')
								,languageId = local.languageId
								,keywords = arguments.event.getValue('keywords_#local.languageId#')
								,pageTitle = arguments.event.getValue('pageTitle_#local.languageId#')
								,pageDesc = arguments.event.getValue('pageDesc_#local.languageId#')
							);
						}
					}
				
					// do redirect 
					if( arguments.event.getValue('redirectName') NEQ arguments.event.getValue('newRedirectName') ){	
						if( len(arguments.event.getValue('newRedirectName')) ){
							local.redirectURL = "#arguments.event.getValue('mc_siteInfo.scheme')#://#arguments.event.getValue('mc_siteInfo.mainhostname')#/?pg=#arguments.event.getTrimValue('pageName')#";
							addRedirect(
								arguments.event.getValue('mc_siteinfo.siteid'),
								local.qryData.qryPage.pageID,
								arguments.event.getValue('newRedirectName'),
								local.redirectURL,
								arguments.event.getValue('redirectID'));
						}
						else{
							local.objAlias = CreateObject("component","model.admin.alias.alias");
							local.objAlias.deleteAlias(redirectIDList=arguments.event.getValue('redirectID'),siteID=arguments.event.getValue('mc_siteInfo.siteID'));
						}
					}
				} else {
					local.strFrom = 'section';
					local.objPage.updateSection(
						siteID=arguments.event.getValue('mc_siteInfo.siteID'),
						ovTemplateID=arguments.event.getValue('newOvTemplateID'),
						ovTemplateIDMobile=arguments.event.getValue('newOvTemplateIDMobile'),
						ovModeID=arguments.event.getValue('newOvModeID'),
						parentSectionID=arguments.event.getValue('newSectionID'),
						sectionName=arguments.event.getTrimValue('sectionName'),
						sectionCode=arguments.event.getTrimValue('sectionCode',''),
						sectionBreadcrumb=arguments.event.getTrimValue('sectionBreadcrumb'),
						siteResourceStatusID=arguments.event.getValue('siteResourceStatusID'),
						inheritPlacements=arguments.event.getValue('inheritPlacements'),
						sectionID=local.qryData.qrySection.sectionID);
				}

				saveMenus(arguments.event);
			} catch (any e) {
				application.objError.sendError(cfcatch=e, objectToDump=local);

				if (structKeyExists(e,"detail") and findNoCase("Invalid template setup", e.detail))
					application.objCommon.redirect('#this.link.message#&errcode=invalidtemplate');
				else 
					application.objCommon.redirect('#this.link.message#');
			}
		</cfscript>
		<cfsavecontent variable="local.data">
			<cfoutput>
				<script language="javascript">
					top.MCModalUtils.hideModal();
					<cfif local.strFrom EQ'section'>
						top.reloadSection();
					<cfelse>
						top.reloadPage(#local.newSectionSRID#);
					</cfif>
								
				</script>
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo") />	
	</cffunction>

	<cffunction name="saveMenus" access="private" output="false" returntype="void">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			local.rc = arguments.event.getCollection();
			local.objPage = CreateObject("component","model.admin.pages.pages");
			local.qryData = local.objPage.getData(arguments.event.getValue('mc_siteInfo.siteID'),arguments.event.getValue('rID'));
		</cfscript>
		<cfloop query="local.qryData.qryTemplateMUT">
			<cfset local.menuID ="newMenu_#local.qryData.qryTemplateMUT.templateUsageTypeID#" />
			<cfif structKeyExists(local.rc,local.menuID)>
				<cfset local.objPage.updateMenu(local.rc[local.menuID], local.qryData.qryTemplateMUT.templateUsageTypeID, local.rc.rID,false) />
			</cfif>
		</cfloop>
		<cfloop query="local.qryData.qryTemplateMobileMUT">
			<cfset local.menuID ="newMenuMobile_#local.qryData.qryTemplateMobileMUT.templateUsageTypeID#" />
			<cfif structKeyExists(local.rc,local.menuID)>
				<cfset local.objPage.updateMenu(local.rc[local.menuID], local.qryData.qryTemplateMobileMUT.templateUsageTypeID, local.rc.rID,true) />
			</cfif>
		</cfloop>
	</cffunction>

	<cffunction name="addRedirect" access="public" output="false" returntype="void" hint="update Document">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="pageID" type="numeric" required="true">
		<cfargument name="redirectName" type="string" required="true">
		<cfargument name="redirectURL" type="string" required="true">
		<cfargument name="oldRedirectID" type="numeric" required="true">
		
		<cfscript>
			var local = structNew();
			local.objAlias = CreateObject("component","model.admin.alias.alias");
			local.redirectID = local.objAlias.insertAlias(
				siteID=arguments.siteID,
				redirectName=arguments.redirectName,
				redirectURL=arguments.redirectURL
			);
			updatePageRedirect(arguments.pageID,local.redirectID);
			if( val(arguments.oldRedirectID) ){
				local.objAlias.deleteAlias(redirectIDList=val(arguments.oldRedirectID),siteID=arguments.siteID);
			}
		</cfscript>
	</cffunction>

	<cffunction name="updatePageRedirect" access="public" output="false" returntype="void" hint="update Document">
		<cfargument name="pageID" type="numeric" required="true">
		<cfargument name="redirectID" type="numeric" required="false">

		<cfset var local = structNew()>
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.update">
			UPDATE dbo.cms_pages 
			SET redirectID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.redirectID#">
			WHERE pageID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.pageID#">
		</cfquery>
	</cffunction>
	
	<cffunction name="addSection" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			local.rc = arguments.event.getCollection();
			local.security.canManageSections = checkRights(arguments.event,'ManageSections');

			if (local.security.canManageSections is not 1)
				application.objCommon.redirect('#this.link.message#&errcode=noaccess');

			local.objPage = CreateObject("component","model.admin.pages.pages");
			local.objSection = CreateObject("component","model.system.platform.section");
			local.getSections = local.objSection.getRecursiveSections(siteID=arguments.event.getValue('mc_siteInfo.siteID'));
			local.qryStatuses = getAvailableResourceStatuses();
			local.qryModes = getAvailableModes();
			local.qryTemplates = getAvailableTemplates(arguments.event.getValue('mc_siteInfo.siteID'));
			local.qryMobileTemplates = getAvailableTemplates(arguments.event.getValue('mc_siteInfo.siteID'), true);
			local.qryMenus = getMenus(arguments.event.getValue('mc_siteInfo.siteID'));

			// param form fields
			arguments.event.paramValue('sectionName','');
			arguments.event.paramValue('sectionCode','');
			arguments.event.paramValue('sectionBreadcrumb','');
			arguments.event.paramValue('siteResourceStatusID',0);
			arguments.event.paramValue('newSectionID',0);
			arguments.event.paramValue('newOvModeID',0);
			arguments.event.paramValue('newOvTemplateID',0);
			arguments.event.paramValue('newOvTemplateIDMobile',0);
			arguments.event.paramValue('inheritPlacements', True);
			arguments.event.paramValue('parentSectionID','');
			arguments.event.paramValue('parentSectionName','');
			
			if( arguments.event.valueExists('rID') ){
				local.qryData = local.objPage.getData(arguments.event.getValue('mc_siteInfo.siteID'),arguments.event.getValue('rID'));
				arguments.event.setValue('parentSectionName' ,local.qryData.qrySection.sectionName);
				arguments.event.setValue('parentSectionID',local.qryData.qrySection.sectionID);
				arguments.event.setValue('newSectionID',arguments.event.getValue('parentSectionID'));
			}
			
			local.formSubmit = "#this.link.insertSection#";

			appendBreadCrumbs(arguments.event,{ link='', text="Add Section" });
		</cfscript>
		
		<cfsavecontent variable="local.data">
			<cfinclude template="frm_editSection.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="insertSection" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		
		<cfscript>
			local.rc = arguments.event.getCollection();
			local.objPage = CreateObject("component","model.admin.pages.pages");
			
			local.newSection = local.objPage.insertSection(
				arguments.event.getValue('mc_siteInfo.siteID'),
				arguments.event.getValue('newOvTemplateID'),
				arguments.event.getValue('newOvTemplateIDMobile'),
				arguments.event.getValue('newOvModeID'),
				arguments.event.getValue('newSectionID'),
				arguments.event.getTrimValue('sectionName'),
				arguments.event.getTrimValue('sectionCode',''),
				arguments.event.getTrimValue('sectionBreadcrumb'),
				arguments.event.getValue('siteResourceStatusID'),
				arguments.event.getValue('inheritPlacements'));
		</cfscript>
		<cflocation addtoken="false" url="#this.link.edit#&rID=#local.newSection#">
		<cfreturn returnAppStruct(local.data,"echo") />	
	</cffunction>

	<cffunction name="checkSectionCodeExists" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="sectionID" type="numeric" required="true">
		<cfargument name="sectionCode" type="string" required="true">
		
		<cfscript>
			var local = structNew();
			local.returnStruct = structNew();
			local.returnStruct.sectionCodeExists = true;
			
			if (REFindNoCase("[^a-z0-9\-_]+", arguments.sectionCode) eq 0) {
				local.sectionInfo = CreateObject("component","model.system.platform.section").getSectionFromSectionCode(siteID=arguments.mcproxy_siteID, sectionCode=arguments.sectionCode);
				local.matchingSectionID = val(local.sectionInfo.sectionID);

				if (local.matchingSectionID GT 0) {
					local.sectionTaken = local.matchingSectionID NEQ arguments.sectionID ? true : false;
				} else {
					local.sectionTaken = false;
				}
				
				if (local.sectionTaken) {
					local.returnStruct.sectionCodeExists = true;
					local.returnStruct.msg = 'Section code already exists.';
				} else {
					local.returnStruct.sectionCodeExists = false;
					local.returnStruct.msg = 'Section code passed.';
				}
			} else {
				local.returnStruct.sectionCodeExists = false;
				local.returnStruct.msg = 'Section code can only contain letters, numbers, hyphens and underscore.';
			}

			local.returnStruct.success	= true;

			return local.returnStruct;
		</cfscript>
	</cffunction>
		
	<cffunction name="addContentPage" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			local.rc = arguments.event.getCollection();
			local.security.canAddPage = checkRights(arguments.event,'AddPage');

			if (local.security.canAddPage is not 1)
				application.objCommon.redirect('#this.link.message#&errcode=noaccess');

			local.objPage = CreateObject("component","model.admin.pages.pages");
			local.objSection = CreateObject("component","model.system.platform.section");

			local.getSections = local.objSection.getRecursiveSections(siteID=arguments.event.getValue('mc_siteInfo.siteID'));
			local.qryModes = getAvailableModes();
			local.qryTemplates = getAvailableTemplates(arguments.event.getValue('mc_siteInfo.siteID'));
			local.qryMobileTemplates = getAvailableTemplates(arguments.event.getValue('mc_siteInfo.siteID'), true);
			local.qryMenus = getMenus(arguments.event.getValue('mc_siteInfo.siteID'));
			local.qryStatuses = getAvailableResourceStatuses();
			local.qryLanguages = getAvailableLanguages();
			local.defaultStatus	= getStatusID('Inactive');

			local.formSubmit = '';
			arguments.event.setValue('pageID',0);
			arguments.event.setValue('pageName','');
			arguments.event.setValue('redirectName', '');
			arguments.event.setValue('redirectID', '');
			arguments.event.setValue('siteResourceStatusID', local.defaultStatus);
			arguments.event.setValue('newSectionID', '');
			arguments.event.setValue('newOvModeID', '');
			arguments.event.setValue('newOvTemplateID', '');
			arguments.event.setValue('newOvTemplateIDMobile', '');
			arguments.event.setValue('inheritPlacements', True);
			
			if( arguments.event.getValue('rID',0) ){
				arguments.event.setValue('newSectionID',local.objPage.getSectionIDFromResourceID(arguments.event.getValue('mc_siteInfo.siteID'),arguments.event.getValue('rID')));
			}
			local.isFromCommunity = int(val(arguments.event.getValue('isFromCommunity',0)));
			local.commSRID = arguments.event.getValue('commSRID',0);
			appendBreadCrumbs(arguments.event,{ link='', text="Add Page" });
		</cfscript>

		<cfquery name="local.getGroups" datasource="#application.dsn.membercentral.dsn#">
			SELECT groupID, isSystemGroup, groupPathSortOrder AS thePath, groupPathExpanded AS thePathexpanded, groupDesc
			FROM dbo.ams_groups 
			WHERE orgID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteInfo.orgID')#">
			AND status <> 'D'
			AND hideOnGroupLists = 0
		</cfquery>
		<cfquery name="local.getFrequentGroups" datasource="#application.dsn.membercentral.dsn#">
			select groupID, groupDesc, thePathExpanded
			from (
				select top 10 srr.groupID, g.groupDesc, g.groupPathExpanded as thePathExpanded, count(*) as numPerms
				from dbo.cms_siteResourceRights srr
				inner join dbo.ams_groups g on g.groupID = srr.groupID
					and g.orgID = <cfqueryparam cfsqltype="cf_sql_integer" value="#application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).orgid#">
					and g.isSystemGroup = 0
					and g.hideOnGroupLists = 0
				where g.status <> 'D' and srr.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.siteID')#">
				group by srr.groupID, g.groupDesc, g.groupPathExpanded
				order by count(*) desc
			) r
			order by numPerms, thePathExpanded
		</cfquery>
		<cfquery name="local.getGroupsSYS" dbtype="query">
			select *
			from [local].getGroups
			where isSystemGroup = 1
			order by thePath
		</cfquery>
		<cfquery name="local.getGroupsNonSYS" dbtype="query">
			select *
			from [local].getGroups
			where isSystemGroup = 0
			order by thePath
		</cfquery>
		<cfquery name="local.getPublicGroup" dbtype="query">
			select *
			from [local].getGroups
			where isSystemGroup = 1
			and thePathexpanded='Public'
		</cfquery>
		
		<cfsavecontent variable="local.data">
			<cfinclude template="frm_editPage.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="insertPage" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="pageName" type="string" required="true">
		<cfargument name="newSectionID" type="numeric" required="true">
		<cfargument name="siteResourceStatusID" required="true" type="numeric" >
		<cfargument name="pageTitle"  required="true"  type="string">
		<cfargument name="keywords" required="true"  type="string">
		<cfargument name="pageDesc" required="true"  type="string">
		<cfargument name="groupID" required="true"  type="string">
		<cfargument name="commSRID" required="true" type="numeric">
		<cfargument name="isFromCommunity" type="boolean" required="false" default="0">

		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>

		<cftry>
			<cfset local.pageAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='PageAdmin',siteID=arguments.mcproxy_siteID)>
			<cfset local.tmpRights = buildRightAssignments(siteResourceID=local.pageAdminSRID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.mcproxy_siteID)>
			<cfif not structKeyExists(local.tmpRights,"AddPage") or local.tmpRights.AddPage is not 1>
				<cfthrow message="invalid request">
			</cfif>
			
			<cfset local.newPageID = CreateObject("component","pages").insertContentPage(
				siteID = arguments.mcproxy_siteID, languageID = 1, sectionID = arguments.newSectionID, ovModeID = 0, ovTemplateID = 0, ovTemplateIDMobile = 0,
				inheritPlacements = 1, pageName = arguments.pageName, pageTitle = arguments.pageTitle, pageDesc = arguments.pageDesc, keywords = arguments.keywords,
				siteResourceStatusID = arguments.siteResourceStatusID, isFromCommunity = arguments.isFromCommunity, commSRID = arguments.commSRID)>

			<cfquery name="local.getpageID" datasource="#application.dsn.membercentral.dsn#">
				select siteResourceID from cms_pages where pageID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.newPageID#">
			</cfquery>
			<cfif arguments.isFromCommunity EQ 0>
				<cfset local.statusID = getStatusID('active')>
				<cfset local.updateStatus = updateResourceStatus(arguments.mcproxy_siteID, local.getpageID.siteResourceID, local.statusID)>
				<cfset createDefaultMainZoneContent(local.getpageID.siteResourceID, arguments.groupID)>
			</cfif>

			<cfset local.returnStruct.success = true>
			<cfset local.returnStruct.rid = local.getpageID.siteResourceID>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.returnStruct.success = false>
			<cfset local.returnStruct.rid = 0>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="createDefaultMainZoneContent" access="private" output="false" returntype="void">
		<cfargument name="siteResourceID" type="numeric" required="true">
		<cfargument name="groupID" type="string" required="true">

		<cfset var local = structNew()>

		<cfquery name="local.qryMainZone" datasource="#application.dsn.memberCentral.dsn#">
			select z.zoneID, z.zonename, z.zoneOrder
			from dbo.cms_pageZones z
			where z.systemOnly = 0
			and z.zonename = 'Main'
		</cfquery>

		<cfquery name="local.createDefaultContent" datasource="#application.dsn.membercentral.dsn#">
			set nocount on;

			declare @resourceTypeID int, @contentID int, @siteResourceID int, @pageID int, @trashID int;
			
			select @resourceTypeID = dbo.fn_getResourceTypeID('UserCreatedContent');
			
			select @pageID = p.pageID
			from dbo.cms_siteResources sr
			inner join dbo.cms_pages p on p.siteResourceID = sr.siteResourceID	
			where sr.siteResourceID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteResourceID#">;
			
			exec dbo.cms_createContentObject
				@siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).siteID#">,
				@resourceTypeID =@resourceTypeID,
				@parentSiteResourceID = null,
				@siteResourceStatusID = 1,
				@isHTML = 1,
				@languageID = <cfqueryparam cfsqltype="cf_sql_integer" value="#session.mcStruct.languageid#">,
				@isActive = 1,
				@contentTitle = 'Main Content',
				@contentDesc = 'Main Content',
				@rawContent = '',
				@memberID = <cfqueryparam  value="#application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).orgID)#" cfsqltype="cf_sql_integer" />,
				@contentID = @contentID OUTPUT,
				@siteResourceID = @siteResourceID OUTPUT;
				
			exec dbo.cms_createPageZoneResource
				@pageID = @pageID,
				@zoneID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.qryMainZone.zoneID#">,
				@siteResourceID = @siteResourceID,
				@pzrID = @trashID OUTPUT;
			
			select @siteResourceID as newSiteResourceId;
		</cfquery>

		<cfset createObject("component","pages").addPermissionForZoneContent(siteResourceID=local.createDefaultContent.newSiteResourceId, groupID=arguments.groupID)>
	</cffunction>

	<cffunction name="reassignContent" access="public" output="false" returntype="struct" hint="Form to Add Section">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			local.objSection = CreateObject("component","model.system.platform.section");

			local.rootSectionID = local.objSection.getRootSectionID(arguments.event.getValue('mc_siteInfo.siteID'));
			local.currentSectionID = local.objSection.getSectionID(arguments.event.getValue('mc_siteInfo.siteID'),arguments.event.getValue('rID'));
			local.getSections = local.objSection.getRecursiveSections(siteID=arguments.event.getValue('mc_siteInfo.siteID'), startSectionID=local.rootSectionID, excludeSectionID=local.currentSectionID);
			local.sectionData = local.objSection.getSectionData(arguments.event.getValue('mc_siteInfo.siteID'),local.currentSectionID);
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_reassign.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="assignContent" access="public" output="false" returntype="struct" hint="Form to Add Section">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			local.objPage = CreateObject("component","model.admin.pages.pages");
			local.objSection = CreateObject("component","model.system.platform.section");
			local.siteID = arguments.event.getValue('mc_siteInfo.siteID');
			local.sectionSRID = local.objSection.getSectionSiteResourceID(local.siteID,arguments.event.getValue('oldParent'));
			local.rootSectionSRID = local.objPage.getRootSectionID(siteID=local.siteID);
		</cfscript>

		<!--- Reassign Content Process --->
		<cfset local.relocate = local.objPage.relocateContent(local.siteID,arguments.event.getValue('oldParent'),arguments.event.getValue('newParent'))>

		<!--- remove oldParent --->
		<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="cms_deleteSiteResourceAndChildren">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.siteID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.sectionSRID#">
		</cfstoredproc>

		<!--- locate user back to list --->
		<cfsavecontent variable="local.data">
			<cfoutput>
				<script type="text/javascript">
					top.MCModalUtils.hideModal();
					top.filterPages();
				</script>
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>
	
	<cffunction name="contentDetails" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			local.rc = arguments.event.getCollection();
		</cfscript>
		<cfsavecontent variable="local.data">
			<cfset local.contentDetailsObj = createObject("component","model.admin.common.modules.contentDetails.contentDetails")>
			<cfset local.contentDetailsObj.controller(this.link.contentDetails, "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mode=stream", this.link.contentDetails,"")>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="getAvailableModes" access="public" output="false" returntype="query" hint="">
		<cfset var local = structNew()>
		<cfquery name="local.qryZones" datasource="#application.dsn.memberCentral.dsn#">
			select m.modeID, m.modeName
			from cms_pageModes m
			order by m.modeID
		</cfquery>
		<cfreturn local.qryZones>
	</cffunction>

	<cffunction name="getMenus" access="public" output="false" returntype="query" hint="">
		<cfargument name="siteID" type="numeric" required="true">
		<cfset var local = structNew()>
		<cfquery name="local.qryMenus" datasource="#application.dsn.memberCentral.dsn#">
			select menuID, siteID, menuName, menuCode, uid, contentID
			from cms_menus 
			where siteid =  <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">
		</cfquery>
		<cfreturn local.qryMenus>
	</cffunction>

	<!---
		Currently, I am only updating the query to the show global mobile templates.
		I believe the side effects need to be considered before standardizing for non-mobile templates.
	--->
	<cffunction name="getAvailableTemplates" access="private" output="false" returntype="query" hint="">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="excludeNonMobileTemplates" type="boolean" required="false" default="false">

		<cfset var local = structNew()>
		<cfquery name="local.data" datasource="#application.dsn.memberCentral.dsn#" result="local.stats">
			SELECT	t.templateID, t.templateDesc,
			templateName = case 
				when (ptt.templateTypeName = 'Application' and t.siteID is null) then t.templateName + ' (Global Application Template)'
				when ptt.templateTypeName = 'Application' then t.templateName + ' (Application Template)'
				when t.siteID is null then t.templateName + ' (Global Template)'
				else  t.templateName end
			FROM		cms_pageTemplates t
			inner join cms_pageTemplateTypes ptt
				on ptt.templateTypeID = t.templateTypeID
				AND	t.status = 'A'
				<cfif (isdefined("arguments.excludeNonMobileTemplates") and arguments.excludeNonMobileTemplates eq true)>
					and t.isMobile = 1
					and	isnull(t.siteID,#arguments.siteID#) = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">
					and ptt.templateTypeName = 'Page'
				<cfelse>
					and	isnull(t.siteID,#arguments.siteID#) = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">
					and ptt.templateTypeName = 'Page'
				</cfif>
			ORDER BY t.siteID desc, ptt.templateTypeName desc, t.templateName
		</cfquery>
		<cfreturn local.data>
	</cffunction>

	<cffunction name="getAvailableLanguages" access="public" output="false" returntype="query" hint="">
		<cfset var local = structNew()>
		<cfquery name="local.qryData" datasource="#application.dsn.memberCentral.dsn#">
			select l.languagename, l.languageID
			from cms_languages l
			order by l.languageName
		</cfquery>
		<cfreturn local.qryData>
	</cffunction>

	<cffunction name="getAvailableResourceStatuses" access="public" output="false" returntype="query" hint="Find Documents and returns XML">
		<cfset var local = structNew()>
		<cfquery name="local.qryData" datasource="#application.dsn.memberCentral.dsn#">
			SELECT siteResourceStatusID, siteResourceStatusDesc
			FROM dbo.cms_siteResourceStatuses
			WHERE siteResourceStatusID <> 3
			ORDER BY siteResourceStatusID;
		</cfquery>
		<cfreturn local.qryData>
	</cffunction>

	<cffunction name="getStatusByID" access="public" output="false" returntype="string" hint="get Status by statusID from getAvailbleResourceStatuses">
		<cfargument name="statuses" type="query" required="true">
		<cfargument name="siteResourceStatusID" type="numeric" required="true">
		<cfset var local = structNew()>
		<cfquery name="local.qryData" dbtype="query">
			select siteResourceStatusDesc
			from arguments.statuses
			where siteResourceStatusID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteResourceStatusID#">
		</cfquery>
		<cfreturn local.qryData.siteResourceStatusDesc>
	</cffunction>

	<cffunction name="getStatusID" access="public" output="false" returntype="numeric">
		<cfargument name="status" type="String" required="true">
		<cfset var local = structNew()>
		<cfquery name="local.data" datasource="#application.dsn.memberCentral.dsn#">
			SELECT dbo.fn_getResourceStatusId(<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.status#">) as statusID
		</cfquery>
		<cfreturn local.data.recordCount ? local.data.statusID : 0 />
	</cffunction>

	<cffunction name="updateResourceStatus" access="public" output="false" returntype="void" hint="update siteResource status">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="siteResourceID" type="numeric" required="true">
		<cfargument name="siteResourceStatusID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.update">
			UPDATE dbo.cms_siteResources
			SET siteResourceStatusID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteResourceStatusID#">
			WHERE siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">
			AND siteResourceID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteResourceID#">
		</cfquery>
	</cffunction>

	<cffunction name="addAppInstance" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
	
		<cfset var local = structNew()>

		<cfset local.security.canAddAppInstance = checkRights(arguments.event,'AddAppInstance')>

		<cfif local.security.canAddAppInstance is not 1>
			<cflocation url="#this.link.message#&errcode=noaccess" addtoken="no">
		</cfif>

		<cfset local.addAppObj = createObject("component","appCreationProcess")>

		<cfsavecontent variable="local.data">
			<cfoutput>#local.addAppObj.controller(arguments.event,this.link.addAppInstance)#</cfoutput>
		</cfsavecontent>

		<cfset appendBreadCrumbs(arguments.event,{ link='', text="Add Application" })>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<!--- ajax Functions --->

	<cffunction name="doSectionMove" access="public" output="false" returntype="struct" hint="Re-Order Fields Upwards">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="sectionSRID" type="numeric" required="true">
		<cfargument name="zoneID" type="numeric" required="true">
		<cfargument name="siteResourceID" type="numeric" required="true">
		<cfargument name="dir" type="string" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="cms_moveZoneResource">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.siteResourceID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.sectionSRID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.zoneID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.dir#">
			</cfstoredproc>
			
			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="removePageResource" access="public" output="false" returntype="struct" hint="remove page site resource">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="pageSRID" type="numeric" required="true">
		<cfargument name="sectionSRID" type="numeric" required="true">
		
		<cfset var local = structNew()>
		
		<cftry>
			<cfset local.tmpRights = buildRightAssignments(siteResourceID=arguments.pageSRID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.mcproxy_siteID)>
			<cfif not structKeyExists(local.tmpRights,"DeletePage") or local.tmpRights.DeletePage is not 1>
				<cfthrow message="invalid request">
			</cfif>

			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="cms_deleteSiteResourceAndChildren">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.pageSRID#">
			</cfstoredproc>
			
			<cfquery name="local.qrySection" datasource="#application.dsn.membercentral.dsn#">
				SELECT sectionName
				FROM dbo.cms_pageSections
				WHERE siteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.sectionSRID#">
			</cfquery>
			
			<cfset local.data.success = true>
			<cfset local.data.sectionName = local.qrySection.sectionName>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
			<cfset local.data.sectionName = "">
		</cfcatch>
		</cftry>
		
		<cfreturn local.data>
	</cffunction>

	<cffunction name="removeSectionResource" access="public" output="false" returntype="struct" hint="remove section site resource">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="sectionSRID" type="numeric" required="true">
		
		<cfset var local = structNew()>
		
		<cftry>
			<cfset local.pageAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='PageAdmin',siteID=arguments.mcproxy_siteID)>
			<cfset local.tmpRights = buildRightAssignments(siteResourceID=local.pageAdminSRID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.mcproxy_siteID)>
			<cfif not structKeyExists(local.tmpRights,"ManageSections") or local.tmpRights.ManageSections is not 1>
				<cfthrow message="invalid request">
			</cfif>

			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="cms_deleteSiteResourceAndChildren">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.sectionSRID#">
			</cfstoredproc>
			
			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>
		
		<cfreturn local.data>
	</cffunction>

	<cffunction name="removeCustomPage" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="mcproxy_siteCode" type="string" required="yes">
		<cfargument name="pageName" type="string" required="true">
		<cfargument name="pageID" type="numeric" required="true">
		<cfargument name="ssrid" type="numeric" required="true">
		
		<cfset var local = structNew()>
		<cfset local.data.success = false>
		<cfset local.data.sectionName = "">
		<cftry>
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="cms_deleteCustomPage">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#" null="no">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.pageID#" null="no">
			</cfstoredproc>
			
			<cfif arguments.ssrid gt 0>
				<cfquery name="local.qrySection" datasource="#application.dsn.membercentral.dsn#">
					SELECT sectionName
					FROM dbo.cms_pageSections
					WHERE siteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.ssrid#">
				</cfquery>
				<cfset local.data.sectionName = local.qrySection.sectionName>
			</cfif>

			<cfsavecontent variable="local.supportEmailContent">
				<cfoutput>
				<p>The following custompage has been deleted.</p>
				<p>Site Code : #arguments.mcproxy_siteCode#</p>
				<p>Page Name : #arguments.pageName#</p>
				</cfoutput>
			</cfsavecontent>

			<cfset local.emailtitle = 'Custom Page Deleted'>
			<cfset local.strReturn = application.objEmailWrapper.sendMailESQ(
				emailfrom={ name="MemberCentral", email="<EMAIL>" },
				emailto=[{ name='', email="<EMAIL>" }],
				emailreplyto="",
				emailsubject="#application.MCEnvironment# - Developer Needed - #local.emailTitle#",
				emailtitle=local.emailTitle,
				emailhtmlcontent=local.supportEmailContent,
				siteID=application.objSiteInfo.getSiteIDFromSiteCode(siteCode='MC'),
				memberID=application.objCommon.getMCSystemMemberID(),
				messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
				sendingSiteResourceID=application.objSiteInfo.getSiteInfo(arguments.mcproxy_siteCode).siteSiteResourceID
			)>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>
		
		<cfreturn local.data>
	</cffunction>

	<cffunction name="doStatusChange" access="public" output="false" returntype="struct" hint="Change Status From Grid List">
		<cfargument name="mcproxy_siteID" type="numeric">
		<cfargument name="siteResourceID" type="numeric">
		<cfargument name="siteResourceStatusID" type="numeric">
		
		<cfset updateResourceStatus(siteID=arguments.mcproxy_siteID, siteResourceID=arguments.siteResourceID, siteResourceStatusID=arguments.siteResourceStatusID)>

		<cfreturn { "success":true }>
	</cffunction>

	<cffunction name="removePZRID" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric">
		<cfargument name="pzrID" type="numeric" required="true">

		<cfset CreateObject("component","pages").removePZR(siteID=arguments.mcproxy_siteID, pzrID=arguments.pzrID)>

		<cfreturn { "success":true }>
	</cffunction>

	<cffunction name="removePSZRID" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric">
		<cfargument name="pszrID" type="numeric" required="true">

		<cfset CreateObject("component","pages").removePSZR(siteID=arguments.mcproxy_siteID, pszrID=arguments.pszrID)>

		<cfreturn { "success":true }>
	</cffunction>

	<!--- content ajax functions --->
	<cffunction name="doPageMove" access="public" output="false" returntype="struct" hint="Re-Order Fields Upwards">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="pageSRID" type="numeric" required="true">
		<cfargument name="contentSRID" type="numeric" required="true">
		<cfargument name="zoneID" type="numeric" required="true">
		<cfargument name="dir" type="string" required="true">
		
		<cfset var local = structNew()>

		<cftry>
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="cms_moveZoneResource">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.contentSRID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.pageSRID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.zoneID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.dir#">
			</cfstoredproc>
			
			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="exportCSV" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>
		<cfset local.exportFileName = "Pages.csv">
		
		<cfstoredproc procedure="cms_pageExport" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteinfo.siteid')#">
			<cfprocparam cfsqltype="cf_sql_integer" value="#session.cfcuser.memberdata.memberID#">
			<cfprocparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('kw','')#">
			<cfif len(arguments.event.getValue('cs',''))>
				<cfprocparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('cs','')#">
			<cfelse>
				<cfprocparam cfsqltype="cf_sql_varchar" null="true">
			</cfif>
			<cfif len(arguments.event.getValue('ce',''))>
				<cfprocparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('ce','')#">
			<cfelse>
				<cfprocparam cfsqltype="cf_sql_varchar" null="true">
			</cfif>
			<cfif len(arguments.event.getValue('ms',''))>
				<cfprocparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('ms','')#">
			<cfelse>
				<cfprocparam cfsqltype="cf_sql_varchar" null="true">
			</cfif>
			<cfif len(arguments.event.getValue('me',''))>
				<cfprocparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('me','')#">
			<cfelse>
				<cfprocparam cfsqltype="cf_sql_varchar" null="true">
			</cfif>
			<cfprocparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('pt','')#">
			<cfprocparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('ps','')#">
			<cfprocparam cfsqltype="cf_sql_bit" value="#arguments.event.getValue('shNP','')#">
			<cfprocparam cfsqltype="cf_sql_varchar" value="#application.objCommon.convertFileSeparator("#local.strFolder.folderPathUNC#/#local.exportFileName#",'\')#">
			<cfprocparam cfsqltype="cf_sql_integer" value="0">
		</cfstoredproc>

		<cfset application.objDocDownload.waitForFileExists(sourceFilePath="#local.strFolder.folderPath#/#local.exportFileName#")>
        <cfset local.docResult = application.objDocDownload.doDownloadDocument(sourceFilePath="#local.strFolder.folderPath#/#local.exportFileName#", displayName=local.exportFileName, deleteSourceFile=1)>
        <cfif not local.docResult>
            <cflocation url="#this.link.list#" addtoken="no">
        </cfif>
	</cffunction>

	<cffunction name="exportJSON" access="public" output="false" returntype="struct">
		<cfargument name="kw" type="string" required="true">
		<cfargument name="cs" type="string" required="true">
		<cfargument name="ce" type="string" required="true">
		<cfargument name="ms" type="string" required="true">
		<cfargument name="me" type="string" required="true">
		<cfargument name="pt" type="string" required="true">
		<cfargument name="ps" type="string" required="true">
		<cfargument name="shNP" type="numeric" required="true">
		<cfargument name="siteID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cfstoredproc procedure="cms_pageExport" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">
			<cfprocparam cfsqltype="cf_sql_integer" value="#session.cfcuser.memberdata.memberID#">
			<cfprocparam cfsqltype="cf_sql_varchar" value="#arguments.kw#">
			<cfif len(arguments.cs)>
				<cfprocparam cfsqltype="cf_sql_varchar" value="#arguments.cs#">
			<cfelse>
				<cfprocparam cfsqltype="cf_sql_varchar" null="true">
			</cfif>
			<cfif len(arguments.ce)>
				<cfprocparam cfsqltype="cf_sql_varchar" value="#arguments.ce#">
			<cfelse>
				<cfprocparam cfsqltype="cf_sql_varchar" null="true">
			</cfif>
			<cfif len(arguments.ms)>
				<cfprocparam cfsqltype="cf_sql_varchar" value="#arguments.ms#">
			<cfelse>
				<cfprocparam cfsqltype="cf_sql_varchar" null="true">
			</cfif>
			<cfif len(arguments.me)>
				<cfprocparam cfsqltype="cf_sql_varchar" value="#arguments.me#">
			<cfelse>
				<cfprocparam cfsqltype="cf_sql_varchar" null="true">
			</cfif>
			<cfprocparam cfsqltype="cf_sql_varchar" value="#arguments.pt#">
			<cfprocparam cfsqltype="cf_sql_varchar" value="#arguments.ps#">
			<cfprocparam cfsqltype="cf_sql_bit" value="#arguments.shNP#">
			<cfprocparam cfsqltype="cf_sql_varchar" value="">
			<cfprocparam cfsqltype="cf_sql_integer" value="1">
		</cfstoredproc>
		
		<cfset local.returnStruct = structnew()>
		<cfset local.returnStruct.success = true>

		<cfreturn local.returnStruct>
	</cffunction>

	<!--- showWebsiteImages() --->
	<cffunction name="showWebsiteImages" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
	
		<cfset local = structNew()>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<h4>Website Images</h4>
				<iframe src="/sitecomponents/COMMON/javascript/ckfinder/263/ckfinder.html" width="100%" height="600" frameborder="0" scrolling="no"></iframe>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="pageExists" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="pageID" type="numeric" required="true">
		<cfargument name="pageName" type="string" required="true">
		
		<cfset var local = structNew()>

		<cftry>
			<cfset local.returnStruct = { pageExists = true }>
		
			<cfif checkForReservedWords(newWord=arguments.pageName)>
				<cfset local.returnStruct.pageExists = true>
			<cfelse>
				<cfquery name="local.data" datasource="#application.dsn.membercentral.dsn#">
					SELECT p.pageID
					FROM dbo.cms_pages AS p
					INNER JOIN dbo.cms_siteResources AS sr ON p.siteResourceID = sr.siteResourceID
					INNER JOIN dbo.cms_siteResourceStatuses AS srs ON sr.siteResourceStatusID = srs.siteResourceStatusID
						AND srs.siteResourceStatusDesc <> 'Deleted'
					WHERE p.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
					AND p.pageName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.pageName#">
					<cfif arguments.pageID gt 0>
						AND p.pageID <> <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.pageID#">
					</cfif>
				</cfquery>
				
				<cfquery name="local.appCheck" datasource="#application.dsn.membercentral.dsn#">
					SELECT applicationTypeID 
					FROM dbo.cms_applicationTypes 
					WHERE suggestedPageName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.pageName#">
					AND allowPageNameChange = 0
				</cfquery>
				
				<cfif local.data.recordCount or local.appCheck.recordcount>
					<cfset local.returnStruct.pageExists = true>
				<cfelse>
					<cfset local.returnStruct.pageExists = false>
				</cfif>
			</cfif>

			<cfset local.returnStruct.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.returnStruct.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="checkForReservedWords" access="public" output="false" returntype="boolean">
		<cfargument name="newWord" type="string" required="true">
		
		<cfset var reservedWords = 'css,images,javascript,swfs,fonts,galleries,storeimages,usercss,userimages,blogimages,featuredimages,memberphotos,memberphotosth,assets,userassets,tsdd,docDownload,storeDocDownload,reportDocDownload,admin,lam,invoices,paymethods,renewsub,seminarweb,super'>
		
		<cfif listFindNoCase(reservedWords,arguments.newWord)>
			<cfreturn true>
		<cfelse>
			<cfreturn false>
		</cfif>
	</cffunction>

	<!--- IMPORT FUNCTIONS --->
	<cffunction name="sampleImportTemplate" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.data = "The sample import file could not be generated. Contact MemberCentral for assistance.">
		
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>
		<cfset local.reportFileName = "ImportTemplate.csv">

		<cfset local.fieldNames = "PageName,Section,PageTitle,Keywords,Description,Published,QuickLink,InheritPlacements,ModeOverride,TemplateOverride,ApplyNoIndex,ApplyNoFollow,ApplyNoArchive">

		<cfquery name="local.qryExport" datasource="#Application.dsn.membercentral.dsn#" result="local.qryExportResult">
			set nocount on;

			IF OBJECT_ID('tempdb..##tmpImportTemplate') IS NOT NULL 
				DROP TABLE ##tmpImportTemplate;
			CREATE TABLE ##tmpImportTemplate (
				autoID int
				<cfloop list="#local.fieldNames#" index="local.thisCol">
					, #local.thisCol# varchar(30)
				</cfloop>
			);
			
			INSERT INTO ##tmpImportTemplate (#local.fieldNames#)
			VALUES ('Req', '', 'Req', '', '', '', '', '', '', '', '', '', '');
			
			DECLARE @selectsql varchar(max) = '
				SELECT #local.fieldNames#, ROW_NUMBER() OVER(order by PageName) as mcCSVorder 
				*FROM* ##tmpImportTemplate';
			EXEC dbo.up_queryToCSV @selectsql=@selectsql, @csvfilename='#application.objCommon.convertFileSeparator("#local.strFolder.folderPathUNC#/#local.reportFileName#",'\')#', @returnColumns=0;

			IF OBJECT_ID('tempdb..##tmpImportTemplate') IS NOT NULL 
				DROP TABLE ##tmpImportTemplate;
		</cfquery>
				
		<cfset application.objDocDownload.waitForFileExists(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#")>
		<cfset application.objDocDownload.doDownloadDocument(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#", displayName=local.reportFileName, deleteSourceFile=1)>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="preProcessImport" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>

		<cfset local.strImport = { resourceType='PageAdmin', importTitle='', processImportLink=buildCurrentLink(arguments.event,"processImport") & "&tab=import", 
									doAgainLink="#this.link.list#&tab=import" }>

		<cfset local.strImport.importDesc = "Map the columns of your import file to the columns for this import.<br/>We have preselected the import column that matches the required column name.">

		<cfset local.strFormFields = { resourceType='PageAdmin' }>

		<cfset local.qryImportColumns = getImportFieldDetails(mode='import')>
		
		<cfset local.impData = createObject("component","model.admin.common.modules.import.import").preProcessImport(event=arguments.event, strImport=local.strImport, 
									strFormFields=local.strFormFields, qryImportColumns=local.qryImportColumns, formFieldName='pagesImportFileName')>
		
		<cfreturn list(event=arguments.event, impData=local.impData)>
	</cffunction>

	<cffunction name="getImportFieldDetails" access="private" output="false" returntype="query">
		<cfargument name="arrImportColumnDetails" type="array" required="false" default="#arrayNew(1)#">
		<cfargument name="mode" type="string" required="true" hint="import or importTemplate">

		<cfset var local = structNew()>
		<cfset local.columnNameRegex = "[^A-Za-z0-9_\-\&\(\)\:\/\s]">

		<cfset local.importFieldList = "PageName|STRING|1,Section|STRING|0,PageTitle|STRING|1,Keywords|STRING|0,Description|STRING|0,Published|BIT|0,QuickLink|STRING|0,InheritPlacements|BIT|0,ModeOverride|STRING|0,TemplateOverride|STRING|0,ApplyNoIndex|BIT|0,ApplyNoFollow|BIT|0,ApplyNoArchive|BIT|0">
		
		<cfquery name="local.qryImportColumns" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;

			IF OBJECT_ID('tempdb..##tmpImportColumnHeaders') IS NOT NULL 
				DROP TABLE ##tmpImportColumnHeaders;
			IF OBJECT_ID('tempdb..##tmpImportColumns') IS NOT NULL 
				DROP TABLE ##tmpImportColumns;
			IF OBJECT_ID('tempdb..##tmpImportColumnDetails') IS NOT NULL 
				DROP TABLE ##tmpImportColumnDetails;
			
			CREATE TABLE ##tmpImportColumnHeaders (headerRowID int, header varchar(200));
			CREATE TABLE ##tmpImportColumns (columnID int IDENTITY(1,1), columnName varchar(500), dataTypeCode varchar(12), isRequired bit, headerRowID int, defaultColValue varchar(max));
			CREATE TABLE ##tmpImportColumnDetails (columnID int, mappedColValue varchar(500), mappedColOverrideValue varchar(max));
			
			INSERT INTO ##tmpImportColumnHeaders (headerRowID, header)
			select 	1, 'Pages Import - Column Mapping';

			<cfif arrayLen(arguments.arrImportColumnDetails)>
				<cfloop array="#arguments.arrImportColumnDetails#" index="local.thisColumn">
					INSERT INTO ##tmpImportColumnDetails (columnID, mappedColValue, mappedColOverrideValue)
					VALUES (#int(val(local.thisColumn.columnID))#,'#replace(local.thisColumn.mappedColValue,"'","''","ALL")#','#replace(local.thisColumn.mappedColOverrideValue,"'","''","ALL")#');
				</cfloop>
			</cfif>
			
			<cfloop list="#local.importFieldList#" index="local.thisField">
				INSERT INTO ##tmpImportColumns (columnName, dataTypeCode, isRequired, headerRowID)
				VALUES ('#listFirst(local.thisField,'|')#', '#listGetAt(local.thisField,2,'|')#', #listGetAt(local.thisField,3,'|')#, 1);
			</cfloop>

			<cfif arguments.mode is 'importTemplate'>
				select columnID, quotename(dbo.fn_regexReplace(columnName,'#local.columnNameRegex#','')) as columnName 
				from ##tmpImportColumns
				where defaultColValue is null
				order by columnID;
			<cfelseif arguments.mode is 'import'>
				select tmpH.header, tmpCol.columnID, dbo.fn_regexReplace(tmpCol.columnName,'#local.columnNameRegex#','') as columnName, tmpCol.dataTypeCode, 
					tmpCol.isRequired, tmpCol.defaultColValue, tmpColDetails.mappedColValue, tmpColDetails.mappedColOverrideValue 
				from ##tmpImportColumns as tmpCol 
				inner join ##tmpImportColumnHeaders as tmpH on tmpH.headerRowID = tmpCol.headerRowID
				left outer join ##tmpImportColumnDetails as tmpColDetails on tmpColDetails.columnID = tmpCol.columnID
				order by tmpCol.headerRowID, tmpCol.columnID;
			<cfelse>
				select columnID, columnName 
				from ##tmpImportColumns
				order by columnID;
			</cfif>

			IF OBJECT_ID('tempdb..##tmpImportColumnHeaders') IS NOT NULL 
				DROP TABLE ##tmpImportColumnHeaders;
			IF OBJECT_ID('tempdb..##tmpImportColumns') IS NOT NULL 
				DROP TABLE ##tmpImportColumns;
			IF OBJECT_ID('tempdb..##tmpImportColumnDetails') IS NOT NULL 
				DROP TABLE ##tmpImportColumnDetails;
		</cfquery>

		<cfreturn local.qryImportColumns>
	</cffunction>

	<cffunction name="processImport" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfsetting requesttimeout="500">

		<cfset local.objPages = CreateObject("component","pages")>		
		<cfset local.qryImportColumns = getImportFieldDetails(mode='importTemplate')>
		<cfset local.strImportResult = local.objPages.importPages(event=arguments.event, qryImportColumns=local.qryImportColumns)>
				
		<cfif not local.strImportResult.success and structKeyExists(local.strImportResult,"strImportDetails") and arrayLen(local.strImportResult.strImportDetails.arrImportColumnDetails)>
			<cfset local.strImport = { resourceType='PageAdmin', importTitle='', 
										importDesc='Map the columns of your import file to the columns for this import.<br/>We have preselected the import column that matches the required column name.', 
										processImportLink=buildCurrentLink(arguments.event,"processImport") & "&tab=import", doAgainLink="#this.link.list#&tab=import" }>

			<cfset local.qryImportColumns = getImportFieldDetails(arrImportColumnDetails=local.strImportResult.strImportDetails.arrImportColumnDetails, mode='import')>

			<cfset local.strImportResult.previousMappingScreen = createObject("component","model.admin.common.modules.import.import").showImportProcessResults(strImport=local.strImport, strFormFields=local.strImportResult.strImportDetails.strFormFields, qryImportColumns=local.qryImportColumns)>
		</cfif>

		<cfset local.impData = local.objPages.showImportResults(strImportResult=local.strImportResult, doAgainURL="#this.link.list#&tab=import")>
		
		<cfreturn list(event=arguments.event, impData=local.impData)>
	</cffunction>

	<cffunction name="message" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfswitch expression="#arguments.event.getValue('errcode','')#">
			<cfcase value="noaccess">
				<cfset local.message = '<h4>We are Sorry...</h4><div><b>You do not have rights to this section.</b></div>'>
			</cfcase>
			<cfcase value="invalidtemplate">
				<cfset local.message = '<h4>Invalid Override Template</h4><div>Selected Override Template has non-Main Zones tied to the direct mode.</div>'>
			</cfcase>
			<cfdefaultcase>
				<cfset local.message = "<b>An error occurred. Try again or contact support for assistance.</b">
			</cfdefaultcase>
		</cfswitch>

		<cfreturn returnAppStruct(local.message,"echo")>
	</cffunction>

	<cffunction name="uploadJSONImportFile" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.data = { "success":false, "itemid":0, "errmsg":"" }>

		<cfif len(arguments.event.getTrimValue('pageJSONFile',''))>
			<cftry>
				<cfset local.newFile = CreateObject("component","model.system.platform.document").uploadFile("form.pageJSONFile")>
				<cfif not local.newFile.uploadComplete>
					<cfset local.data["errmsg"] = "An error occured while uploading the file.">
				<cfelseif local.newFile.ServerFileExt neq "json">
					<cffile action="delete" file="#local.newFile.ServerDirectory#/#local.newFile.ServerFile#">
					<cfset local.data["errmsg"] = "Uploaded file was not a JSON file.">
				<cfelse>
					<cffile action="read" file="#local.newFile.ServerDirectory#/#local.newFile.ServerFile#" variable="local.pageJSONContent">

					<cfif not IsJSON(local.pageJSONContent)>
						<cfset local.data["errmsg"] = "The file content is not in valid JSON format.">
					<cfelse>
						<cfset local.resultStruct = CreateObject("component","pages").addToImportPagesJSONQueue(siteID=arguments.event.getValue('mc_siteinfo.siteID'),
							itemGroupUID=arguments.event.getValue('itemGroupUID'), pageJSONContent=local.pageJSONContent)>

						<cfset local.data["success"] = local.resultStruct.success>
						<cfset local.data["itemid"] = local.resultStruct.itemid>
					</cfif>
				</cfif>
			<cfcatch type="Any">
				<cfset application.objError.sendError(cfcatch=cfcatch)>
				<cffile action="delete" file="#local.newFile.ServerDirectory#/#local.newFile.ServerFile#">
			</cfcatch>
			</cftry>
		</cfif>

		<cfreturn returnAppStruct(serializeJSON(local.data),'echo')>
	</cffunction>
	<cffunction name="doCopyPage" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			local.objPage = CreateObject("component","model.admin.pages.pages");
			local.siteID = arguments.event.getValue('mc_siteInfo.siteID');
			local.sourcePageSRID = arguments.event.getValue('rID',0);
			local.pageName = arguments.event.getValue('pageName','');
			local.qryData = local.objPage.getData(local.siteID,local.sourcePageSRID);
			local.siteResourceStatusID = local.qryData.qryResourceData.siteResourceStatusID;
		</cfscript>

		<cftry>
			<cfif local.qryData.qryResourceData.resourceType neq "UserCreatedPage">
				<cfthrow message="This operation only allowed for pages of type UserCreatedPage.">
			</cfif>

			<!--- inserting new content page --->
			<cfset local.newPageID = local.objPage.insertContentPage(siteID = local.siteID, languageID = val(local.qryData.qryPageLanguages.languageID),
				sectionID = val(local.qryData.qryPage.sectionID), ovModeID = val(local.qryData.qryPage.ovModeID), ovTemplateID = val(local.qryData.qryPage.ovTemplateID),
				ovTemplateIDMobile = val(local.qryData.qryPage.ovTemplateIDMobile), inheritPlacements = local.qryData.qryPage.inheritPlacements,
				pageName = local.pageName, pageTitle = local.qryData.qryPageLanguages.pageTitle, pageDesc = local.qryData.qryPageLanguages.pageDesc,
				keywords = local.qryData.qryPageLanguages.keywords, siteResourceStatusID = local.siteResourceStatusID)>
			
			<cfquery name="local.qryPageSR" datasource="#application.dsn.membercentral.dsn#">
				select cp.siteResourceID, csr.resourceTypeID
				from cms_pages cp
				inner join dbo.cms_siteResources csr on csr.siteResourceID = cp.siteResourceID
				where cp.pageID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.newPageID#">
			</cfquery>

			<cfset local.updateStatus = updateResourceStatus(local.siteID, local.qryPageSR.siteResourceID, local.siteResourceStatusID)>

			<!--- copying menu usages default --->
			<cfloop query="local.qryData.qryTemplateMUT">
				<cfif len(local.qryData.qryTemplateMUT.menuID)>
					<cfset local.objPage.updateMenu(local.qryData.qryTemplateMUT.menuID, local.qryData.qryTemplateMUT.templateUsageTypeID, local.qryPageSR.siteResourceID, false) />
				</cfif>
			</cfloop>
			
			<!--- copying menu usages for mobile --->
			<cfloop query="local.qryData.qryTemplateMobileMUT">
				<cfif len(local.qryData.qryTemplateMobileMUT.menuID)>
					<cfset local.objPage.updateMenu(local.qryData.qryTemplateMobileMUT.menuID, local.qryData.qryTemplateMobileMUT.templateUsageTypeID, local.qryPageSR.siteResourceID, true) />
				</cfif>
			</cfloop>

			<cfquery name="local.updatePage" datasource="#application.dsn.membercentral.dsn#">
				UPDATE dbo.cms_pages
				SET pageDirectives = <cfqueryparam cfsqltype="cf_sql_varchar" value="#local.qryData.qryPage.pageDirectives#">,
				<cfif len(local.qryData.qryPage.dateUnavailable)>
					dateUnavailable = <cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.qryData.qryPage.dateUnavailable#">
				<cfelse>
					dateUnavailable = <cfqueryparam cfsqltype="cf_sql_timestamp" null="true">
				</cfif>
				WHERE pageID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.newPageID#">
				AND siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.siteID#">
			</cfquery>

			<!--- copying zone and content objects --->
			<cfstoredproc procedure="cms_copyPageZoneAndContents" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#local.siteID#">
				<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#local.newPageID#">
				<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#local.sourcePageSRID#">
				<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#session.cfcuser.memberdata.memberID#">
			</cfstoredproc>

			<!--- copying featured image. same config, so just creating an entry in usage --->
			<cfif val(local.qryData.qryPage.pageFeatureImageID)>
				<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="cms_createFeaturedImageUsage">
					<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#val(local.qryData.qryPage.pageFeatureImageID)#">
					<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#val(local.qryData.qryPage.websitePageFeatureImageConfigID)#">
					<cfprocparam type="IN" cfsqltype="CF_SQL_VARCHAR" value="websitePage">
					<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#local.newPageID#">
					<cfprocparam type="OUT" cfsqltype="CF_SQL_INTEGER" variable="local.featureImageUsageID">
				</cfstoredproc>
			</cfif>

			<cfsavecontent variable="local.data">
				<cfoutput>
					<script language="javascript">
						top.MCModalUtils.hideModal();
						top.$('.modal-backdrop').remove();
						top.editAction(#local.qryPageSR.siteResourceID#,'page');	
					</script>
				</cfoutput>
			</cfsavecontent>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfsavecontent variable="local.data">
				<cfoutput>
					<div class="alert alert-danger">
						<cfif findNoCase("only allowed for pages of type UserCreatedPage", cfcatch.message)>#cfcatch.message#<cfelse>Some error occured while copying this page.</cfif>
					</div>
					<script language="javascript">
						top.$('##MCModalFooter').removeClass('d-none');
						top.$("##btnMCModalSave").remove();
					</script>
				</cfoutput>
			</cfsavecontent>
		</cfcatch>
		</cftry>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="getReadyToProcessPageExportJSONJobsCount" access="private" output="false" returntype="numeric">
		<cfargument name="siteID" type="numeric" required="yes">

		<cfset var local = structNew()>
		<cfset local.jobsCount = 0>

		<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser)>
			<cfquery name="local.qryReadyToProcessPageExportJSONJobs" datasource="#application.dsn.platformQueue.dsn#">
				set nocount on;

				declare @siteID int, @statusReady int, @queueStatus varchar(60);
				set @siteID = <cfqueryparam value="#arguments.siteID#" cfsqltype="CF_SQL_INTEGER">;
				set @queueStatus = 'ReadyToProcess';

				select @statusReady = tqs.queueStatusID 
				from dbo.tblQueueStatuses as tqs
				inner join dbo.tblQueueTypes as tq on tq.queueTypeID = tqs.queueTypeID
				where tq.queueType = 'exportPagesJSON'
				and tqs.queueStatus = @queueStatus;

				select count(1) as itemCount
				from platformQueue.dbo.queue_exportPagesJSON
				where siteID = @siteID
				and statusID = @statusReady
			</cfquery>

			<cfset local.jobsCount = local.qryReadyToProcessPageExportJSONJobs.itemCount>
		</cfif>

		<cfreturn local.jobsCount>
	</cffunction>	

	<cffunction name="exportPage" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.rc = arguments.event.getCollection()>
		<cfset local.rID = arguments.event.getValue('rID')>
		<cfset local.siteID = arguments.event.getValue('mc_siteInfo.siteID')>
		<cfset local.arrKeysToPopulate = ["kw","shNP","ce","cs","me","ms","pt","ps"]>
		
		<cfset this.link.exportCSV = buildCurrentLink(arguments.event,"exportCSV") & "&mode=stream">
		<cfset this.link.exportPermission = buildCurrentLink(arguments.event,"exportPermission") & "&mode=stream">
		<cfset this.link.exportJSON = buildCurrentLink(arguments.event,"exportJSON") & "&mode=stream">
		<cfset local.qryReadyToProcessPageExportJSONJobsCount = getReadyToProcessPageExportJSONJobsCount(siteID=arguments.event.getValue('mc_siteinfo.siteID'))>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_export_list.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	<cffunction name="exportPermission" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>
		<cfset local.exportFileName = "PagesPermission.csv">
		
		<cfstoredproc procedure="cms_pageExport" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteinfo.siteid')#">
			<cfprocparam cfsqltype="cf_sql_integer" value="#session.cfcuser.memberdata.memberID#">
			<cfprocparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('kw','')#">
			<cfif len(arguments.event.getValue('cs',''))>
				<cfprocparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('cs','')#">
			<cfelse>
				<cfprocparam cfsqltype="cf_sql_varchar" null="true">
			</cfif>
			<cfif len(arguments.event.getValue('ce',''))>
				<cfprocparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('ce','')#">
			<cfelse>
				<cfprocparam cfsqltype="cf_sql_varchar" null="true">
			</cfif>
			<cfif len(arguments.event.getValue('ms',''))>
				<cfprocparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('ms','')#">
			<cfelse>
				<cfprocparam cfsqltype="cf_sql_varchar" null="true">
			</cfif>
			<cfif len(arguments.event.getValue('me',''))>
				<cfprocparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('me','')#">
			<cfelse>
				<cfprocparam cfsqltype="cf_sql_varchar" null="true">
			</cfif>
			<cfprocparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('pt','')#">
			<cfprocparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('ps','')#">
			<cfprocparam cfsqltype="cf_sql_bit" value="#arguments.event.getValue('shNP','')#">
			<cfprocparam cfsqltype="cf_sql_varchar" value="#application.objCommon.convertFileSeparator("#local.strFolder.folderPathUNC#/#local.exportFileName#",'\')#">
			<cfprocparam cfsqltype="cf_sql_integer" value="2">
		</cfstoredproc>

		<cfset application.objDocDownload.waitForFileExists(sourceFilePath="#local.strFolder.folderPath#/#local.exportFileName#")>
        <cfset local.docResult = application.objDocDownload.doDownloadDocument(sourceFilePath="#local.strFolder.folderPath#/#local.exportFileName#", displayName=local.exportFileName, deleteSourceFile=1)>
		<cfif not local.docResult>
            <cflocation url="#this.link.list#" addtoken="no">
        </cfif>
	</cffunction>
	<cffunction name="addPage" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			local.rc = arguments.event.getCollection();
			local.ssRID = local.rc.ssRID;
			local.rID = local.rc.ssRID;
			local.isFromCommunity = local.rc.isFromCommunity;
			local.returnToAppAdmin = local.rc.returnToAppAdmin;
			local.appTypeID = local.rc.appTypeID;
			local.security.canAddPage = checkRights(arguments.event,'AddPage');
			local.security.canAddAppInstance = checkRights(arguments.event,'AddAppInstance');
			local.security.canManageSections = checkRights(arguments.event,'ManageSections');
			if(local.isFromCommunity EQ 1)
				local.commSRID = local.rc.commSRID;
			if (local.security.canAddPage is not 1 and local.security.canAddAppInstance is not 1 and local.security.canManageSections is not 1)
				application.objCommon.redirect('#this.link.message#&errcode=noaccess');

			appendBreadCrumbs(arguments.event,{ link='', text="Add Page" });
		</cfscript>
		<cfset local.appTypes = createObject("component","appCreationProcess").getAvailableApplicationTypes(siteID=arguments.event.getValue('mc_siteInfo.siteID'), isSuperUser=application.objUser.isSuperUser(cfcuser=session.cfcuser),isFromCommunity=local.isFromCommunity)>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_addPage.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

</cfcomponent>