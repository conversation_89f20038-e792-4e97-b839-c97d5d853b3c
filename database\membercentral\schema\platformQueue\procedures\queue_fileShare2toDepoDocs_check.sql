ALTER PROC dbo.queue_fileShare2toDepoDocs_check

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @issueCount INT, @timeToUse DATETIME, @queueTypeID INT, @queueStatusID INT, @nextQueueStatusID INT, 
		@itemAsStr VARCHAR(60), @xmlMessage xml, @errorSubject VARCHAR(400), @errorTitle VARCHAR(400);
	SELECT @queueTypeID = queueTypeID FROM dbo.tblQueueTypes WHERE queueType = 'fileShare2toDepoDocs';

	-- fileShare2toDepoDocs / processing autoreset to readyToProcess
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -10, GETDATE());
	SELECT @queueStatusID = queuestatusID FROM dbo.tblQueueStatuses WHERE queueTypeID = @queueTypeID AND queueStatus = 'processing';
	SELECT @nextQueueStatusID = queuestatusID FROM dbo.tblQueueStatuses WHERE queueTypeID = @queueTypeID AND queueStatus = 'readyToProcess';
	SELECT @issueCount = count(itemID) FROM dbo.queue_fileShare2toDepoDocs WHERE statusID = @queueStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.queue_fileShare2toDepoDocs
		SET statusID = @nextQueueStatusID, 
			dateUpdated = GETDATE()
		WHERE statusID = @queueStatusID 
		AND dateUpdated < @timeToUse;

		SET @errorTitle = 'fileShare2toDepoDocs Queue Issue';
		SET @errorSubject = 'fileShare2toDepoDocs queue moved items from processing to readyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- fileShare2toDepoDocs / readyToProcess resend message
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -220, GETDATE());
	SELECT @queueStatusID = queuestatusID FROM dbo.tblQueueStatuses WHERE queueTypeID = @queueTypeID AND queueStatus = 'readyToProcess';
	SELECT @issueCount = count(itemID) FROM dbo.queue_fileShare2toDepoDocs WHERE statusID = @queueStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.queue_fileShare2toDepoDocs
		SET dateUpdated = GETDATE()
			OUTPUT inserted.itemID 
			INTO #tmpCheckQueueMsgs (itemAsStr)
		WHERE statusID = @queueStatusID 
		AND dateUpdated < @timeToUse;

		SELECT @itemAsStr = MIN(itemAsStr) FROM #tmpCheckQueueMsgs;
		WHILE @itemAsStr IS NOT NULL BEGIN
			SET @xmlMessage = '<mc i="' + @itemAsStr + '" />';
			EXEC dbo.queue_fileShare2toDepoDocs_sendMessage @xmlMessage=@xmlMessage;
			SELECT @itemAsStr = MIN(itemAsStr) FROM #tmpCheckQueueMsgs WHERE itemAsStr > @itemAsStr;
		END

		TRUNCATE TABLE #tmpCheckQueueMsgs;

		SET @errorTitle = 'fileShare2toDepoDocs Queue Issue';
		SET @errorSubject = 'fileShare2toDepoDocs queue resent messages in readyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- fileShare2toDepoDocs catchall
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(hour, -4, GETDATE());
	SELECT @issueCount = count(itemID) FROM dbo.queue_fileShare2toDepoDocs WHERE dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		SET @errorTitle = 'fileShare2toDepoDocs Queue Issue';
		SET @errorSubject = 'fileShare2toDepoDocs queue has items last updated more than 4 hours ago';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
