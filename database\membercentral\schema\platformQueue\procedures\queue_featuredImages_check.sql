ALTER PROC dbo.queue_featuredImages_check

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @issueCount INT, @timeToUse DATETIME, @queueTypeID INT, @queueStatusID INT, @nextQueueStatusID INT, 
		@errorSubject VARCHAR(400), @errorTitle VARCHAR(400);
	SELECT @queueTypeID = queueTypeID FROM dbo.tblQueueTypes WHERE queueType = 'featuredImages';

	-- featuredImages / grabbedForProcessing autoreset to readyToProcess
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -10, GETDATE());
	SELECT @queueStatusID = queuestatusID FROM dbo.tblQueueStatuses WHERE queueTypeID = @queueTypeID AND queueStatus = 'grabbedForProcessing';
	SELECT @nextQueueStatusID = queuestatusID FROM dbo.tblQueueStatuses WHERE queueTypeID = @queueTypeID AND queueStatus = 'readyToProcess';
	SELECT @issueCount = count(itemID) FROM dbo.queue_featuredImages WHERE statusID = @queueStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.queue_featuredImages
		SET statusID = @nextQueueStatusID, 
			dateUpdated = GETDATE()
		WHERE statusID = @queueStatusID 
		AND dateUpdated < @timeToUse;

		EXEC membercentral.dbo.sched_resumeTask @name='Process Featured Images Queue', @engine='MCLuceeLinux';

		SET @errorTitle = 'FeaturedImages Queue Issue';
		SET @errorSubject = 'FeaturedImages queue moved items from grabbedForProcessing to readyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- featuredImages / processing autoreset to readyToProcess
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -10, GETDATE());
	SELECT @queueStatusID = queuestatusID FROM dbo.tblQueueStatuses WHERE queueTypeID = @queueTypeID AND queueStatus = 'processing';
	SELECT @nextQueueStatusID = queuestatusID FROM dbo.tblQueueStatuses WHERE queueTypeID = @queueTypeID AND queueStatus = 'readyToProcess';
	SELECT @issueCount = count(itemID) FROM dbo.queue_featuredImages WHERE statusID = @queueStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.queue_featuredImages
		SET statusID = @nextQueueStatusID, 
			dateUpdated = GETDATE()
		WHERE statusID = @queueStatusID 
		AND dateUpdated < @timeToUse;

		EXEC membercentral.dbo.sched_resumeTask @name='Process Featured Images Queue', @engine='MCLuceeLinux';

		SET @errorTitle = 'FeaturedImages Queue Issue';
		SET @errorSubject = 'FeaturedImages queue moved items from processing to readyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- featuredImages catchall
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(hour, -4, GETDATE())
	SELECT @issueCount = count(itemID) FROM dbo.queue_featuredImages WHERE dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		SET @errorTitle = 'FeaturedImages Queue Issue';
		SET @errorSubject = 'FeaturedImages queue has items last updated more than 4 hours ago';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
