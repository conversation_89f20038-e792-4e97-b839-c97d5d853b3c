﻿<cfsavecontent variable="local.pageJS">
<cfoutput>
<cfif local.keyExists("strRevenueGLAcctWidget")>
	#local.strRevenueGLAcctWidget.js#
</cfif>
<script>
	function saveRateCardConfig(){
		mca_hideAlert('err_ratecardconfig');
		if(!$('##divGLAcctSelector').hasClass('d-none'))
			returnToRateCardConfig();

		top.$('##btnMCModalSave').prop('disabled',true);
		var arrReq = new Array();
		var runs = $.trim($('##runs').val());
		var pricePerRun = $.trim($('##pricePerRun').val());
		var glAccountID = $.trim($('##revenueGLAccountID').val());

		if( $.trim($('##configName').val()).length == 0) arrReq.push('Enter the Size/Placement Name.');
		if (runs == '' || runs == 0) arrReq.push('Enter the Number of Runs.');
		else if (isNaN(runs) || Number(runs) != parseInt(runs)) arrReq.push('Enter a Valid Value for Number of Runs.');
		if (pricePerRun == '') arrReq.push('Enter the Price Per Run.');
		if(glAccountID == '' || glAccountID == 0) arrReq.push('Select a Revenue GL Account.');

		if (arrReq.length > 0) {
			mca_showAlert('err_ratecardconfig', arrReq.join('<br/>'));
			top.$('##btnMCModalSave').prop('disabled',false);
		}
		else {
			var fd = $('##frmRateCardConfig').serializeArray();
			$('##divFormArea').addClass('d-none');
			$("##divFormSubmitArea").html(mca_getLoadingHTML()).removeClass('d-none').load('#local.saveRateCardConfigLink#', fd, onSaveComplete);
		}

		return false;
	}
	function onSaveComplete(r){
		let response = JSON.parse(r);
		if (response.success){
			top.reloadRateCardsTable();
			top.MCModalUtils.hideModal();
		}
		else {
			alert('An error occured while trying to save the size/placement.');
			top.$('##btnMCModalSave').prop('disabled',false);
			$('##divFormSubmitArea').html('').addClass('d-none');
			$('##divFormArea').removeClass('d-none');
		}
	}
	function formatInt(num) {
		var i = parseInt(num);
		return (isNaN(i)) ? 0 : i;
	}
	function returnToRateCardConfig() {
		$('##divFormArea').show();
		$('##divGLAcctSelector').addClass('d-none');
		toggleGLASelectorGridArea(false,true);
	}
</script>
</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.pageJS)#" />

<cfoutput>
<div class="row no-gutters" id="divFormArea">
	<div class="col-12">
		<form name="frmRateCardConfig" id="frmRateCardConfig" class="p-2" onsubmit="return saveRateCardConfig();">
			<input type="hidden" name="rateCardID" id="rateCardID" value="#local.rateCardID#" />
			<input type="hidden" name="rateCardConfigID" id="rateCardConfigID" value="#local.rateCardConfigID#" />

			<div id="err_ratecardconfig" class="alert alert-danger d-none"></div>
			<div class="form-label-group">
				<input type="text" name="configName" id="configName" value="#htmleditformat(local.qryRateCardConfig.configName)#" class="form-control" autocomplete="off" maxlength="250">
				<label for="configName">Name *</label>
			</div>
			<div class="form-group row no-gutters">
				<div class="col pr-1">
					<div class="form-label-group">
						<input type="text" name="runs" id="runs" value="#local.qryRateCardConfig.runs#" class="form-control" autocomplete="off" maxlength="10" onblur="if (this.value.length > 0) this.value=formatInt(this.value);">
						<label for="runs">Runs *</label>
					</div>
				</div>
				<div class="col pl-1">
					<div class="input-group flex-nowrap">
						<div class="input-group-prepend">
							<span class="input-group-text px-3">$</span>
						</div>
						<div class="form-label-group flex-grow-1 mb-0">
							<input type="text" name="pricePerRun" id="pricePerRun" value="#local.qryRateCardConfig.pricePerRun#" class="form-control" autocomplete="off" maxlength="19" onblur="if (this.value.length > 0) this.value=formatCurrency(this.value);">
							<label for="pricePerRun">Price Per Run *</label>
						</div>
					</div>
				</div>
			</div>
			<div class="form-label-group">
				<select name="allowRunOverride" id="allowRunOverride" class="form-control">
					<option value="0" <cfif not val(local.qryRateCardConfig.allowRunOverride)>selected</cfif>>No</option>
					<option value="1" <cfif val(local.qryRateCardConfig.allowRunOverride)>selected</cfif>>Yes</option>
				</select>
				<label for="allowRunOverride">Allow Run Override</label>
			</div>
			<div class="form-label-group">
				<select name="allowPriceOverride" id="allowPriceOverride" class="form-control">
					<option value="0" <cfif not val(local.qryRateCardConfig.allowPriceOverride)>selected</cfif>>No</option>
					<option value="1" <cfif val(local.qryRateCardConfig.allowPriceOverride)>selected</cfif>>Yes</option>
				</select>
				<label for="allowPriceOverride">Allow Price Override</label>
			</div>
			<div class="form-group">
				#local.strRevenueGLAcctWidget.html#
			</div>
			<button type="submit" class="d-none">Save</button>
		</form>
	</div>
</div>
<div id="divFormSubmitArea" class="d-none"></div>
</cfoutput>