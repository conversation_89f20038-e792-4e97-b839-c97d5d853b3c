ALTER PROC dbo.queue_emailExtMergeCode_check

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @issueCount INT, @timeToUse DATETIME, @queueTypeID INT, @queueStatusID INT, @nextQueueStatusID INT, 
		@recipientIDDataColumnID int, @messageIDDataColumnID int, @cancelledEmailStatusID int,
		@itemAsStr VARCHAR(60), @xmlMessage xml, @errorSubject VARCHAR(400), @errorTitle VARCHAR(400);
	SELECT @queueTypeID = queueTypeID FROM dbo.tblQueueTypes WHERE queueType = 'emailExtMergeCode';

	select @recipientIDDataColumnID = columnID from dbo.tblQueueTypeDataColumns where queueTypeID = @queueTypeID and columnName = 'MCRecipientID';
	select @messageIDDataColumnID = columnID from dbo.tblQueueTypeDataColumns where queueTypeID = @queueTypeID and columnName = 'MCMessageID';
	SELECT @cancelledEmailStatusID = statusID FROM platformMail.dbo.email_statuses WHERE statusCode = 'C';

	-- EmailExtMergeCode / insertingItems autoreset to readyToProcess
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -45, GETDATE());
	SELECT @queueStatusID = queuestatusID FROM dbo.tblQueueStatuses WHERE queueTypeID = @queueTypeID AND queueStatus = 'insertingItems';
	SELECT @nextQueueStatusID = queuestatusID FROM dbo.tblQueueStatuses WHERE queueTypeID = @queueTypeID AND queueStatus = 'readyToProcess';
	SELECT @issueCount = count(itemUID) FROM dbo.tblQueueItems WHERE queueStatusID = @queueStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.tblQueueItems
		SET queueStatusID = @nextQueueStatusID, 
			dateUpdated = GETDATE()
		WHERE queueStatusID = @queueStatusID 
		AND dateUpdated < @timeToUse;

		SET @errorTitle = 'EmailExtMergeCode Queue Issue';
		SET @errorSubject = 'EmailExtMergeCode queue moved items from insertingItems to readyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- EmailExtMergeCode / grabbedForProcessing autoreset to readyToProcess
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -10, GETDATE());
	SELECT @queueStatusID = queuestatusID FROM dbo.tblQueueStatuses WHERE queueTypeID = @queueTypeID AND queueStatus = 'grabbedForProcessing';
	SELECT @nextQueueStatusID = queuestatusID FROM dbo.tblQueueStatuses WHERE queueTypeID = @queueTypeID AND queueStatus = 'readyToProcess';
	SELECT @issueCount = count(itemUID) FROM dbo.tblQueueItems WHERE queueStatusID = @queueStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.tblQueueItems
		SET queueStatusID = @nextQueueStatusID, 
			dateUpdated = GETDATE(),
			jobUID = null,
			jobDateStarted = null
		WHERE queueStatusID = @queueStatusID 
		AND dateUpdated < @timeToUse;

		SET @errorTitle = 'EmailExtMergeCode Queue Issue';
		SET @errorSubject = 'EmailExtMergeCode queue moved items from grabbedForProcessing to readyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- EmailExtMergeCode / processing autoreset to readyToProcess
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -10, GETDATE());
	SELECT @queueStatusID = queuestatusID FROM dbo.tblQueueStatuses WHERE queueTypeID = @queueTypeID AND queueStatus = 'processing';
	SELECT @nextQueueStatusID = queuestatusID FROM dbo.tblQueueStatuses WHERE queueTypeID = @queueTypeID AND queueStatus = 'readyToProcess';
	SELECT @issueCount = count(itemUID) FROM dbo.tblQueueItems WHERE queueStatusID = @queueStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.tblQueueItems
		SET queueStatusID = @nextQueueStatusID, 
			dateUpdated = GETDATE(),
			jobUID = null,
			jobDateStarted = null
		WHERE queueStatusID = @queueStatusID 
		AND dateUpdated < @timeToUse;

		SET @errorTitle = 'EmailExtMergeCode Queue Issue';
		SET @errorSubject = 'EmailExtMergeCode queue moved items from processing to readyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- cancel readyToProcess items related to recipients or messages that have been cancelled/deleted
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -10, GETDATE());
	SELECT @queueStatusID = queuestatusID FROM dbo.tblQueueStatuses WHERE queueTypeID = @queueTypeID AND queueStatus = 'readyToProcess';
	SELECT @nextQueueStatusID = queuestatusID FROM dbo.tblQueueStatuses WHERE queueTypeID = @queueTypeID AND queueStatus = 'cancelled';
	
	SELECT @issueCount = count(*)
	from dbo.tblQueueItems as qi2
	INNER JOIN dbo.tblQueueItemData as qid2 
		on qi2.itemUID = qid2.itemUID and qid2.columnID = @recipientIDDataColumnID
		and qi2.queueStatusID = @queueStatusID
	inner join platformMail.dbo.email_messageRecipientHistory as mrh2
		on mrh2.recipientID = qid2.columnValueInteger
	INNER JOIN platformMail.dbo.email_messages as m2 on m2.siteID = mrh2.siteID
		and m2.messageID = mrh2.messageID
	where m2.status = 'D' or mrh2.emailStatusID = @cancelledEmailStatusID;

	IF @issueCount > 0 BEGIN
		UPDATE qi
		SET queueStatusID = @nextQueueStatusID, 
			dateUpdated = GETDATE(),
			jobUID = null,
			jobDateStarted = null
		from dbo.tblQueueItems qi
		inner join (
			SELECT qi2.itemUID
			from dbo.tblQueueItems as qi2
			INNER JOIN dbo.tblQueueItemData as qid2 
				on qi2.itemUID = qid2.itemUID and qid2.columnID = @recipientIDDataColumnID
				and qi2.queueStatusID = @queueStatusID
			inner join platformMail.dbo.email_messageRecipientHistory as mrh2
				on mrh2.recipientID = qid2.columnValueInteger
			INNER JOIN platformMail.dbo.email_messages as m2 on m2.siteID = mrh2.siteID
				and m2.messageID = mrh2.messageID
			where m2.status = 'D' or mrh2.emailStatusID = @cancelledEmailStatusID
		) as temp on temp.itemUID = qi.itemUID;
	END

	-- EmailExtMergeCode catchall
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(hour, -4, GETDATE())
	SELECT @issueCount = count(qi.itemUID)
		FROM dbo.tblQueueItems AS qi 
		INNER JOIN dbo.tblQueueStatuses AS qis ON qis.queueStatusID = qi.queueStatusID
		INNER JOIN dbo.tblQueueTypes AS qt ON qt.queueTypeID = qis.queueTypeID
		WHERE qt.queueTypeID = @queueTypeID
		AND(
			(qi.dateUpdated < @timeToUse AND qi.jobUID is null)
			OR
			qi.JobDateStarted < @timeToUse
		);
	IF @issueCount > 0 BEGIN
		SET @errorTitle = 'EmailExtMergeCode Queue Issue';
		SET @errorSubject = 'EmailExtMergeCode queue has items last updated more than 4 hours ago';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
