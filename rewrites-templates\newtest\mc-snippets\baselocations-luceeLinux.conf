    ################### LOCATION: WEBSERVICES #####################
    location /webservices/ {

        rewrite (?i)^/webservices/expologic.cfc /webservices/ExpoLogic.cfc;
        rewrite (?i)^/webservices/lists.cfc /webservices/lists.cfc;
        rewrite (?i)^/webservices/cflists.cfc /webservices/CFlists.cfc;
        rewrite (?i)^/webservices/listsdocument.cfc /webservices/listsDocument.cfc;

        ################### WEBSERVICES HANDLER #####################
        location ~* (\.cfm|\.cfc)(.*)$ {
            proxy_pass  "http://MCDockerTestServer";
            include     /opt/sites/test/wwwroot/mc-automanaged/newtest/mc-snippets/membercentral-proxy.conf;
            proxy_set_header X-MC-InternalRequest $isinternalrequest;
        }
    }

    ################### LOCATION: ROOT #####################
    location / {

        ################### CF HANDLER #####################
        location = / {
            proxy_pass  "http://MCDockerTestServer";
            include     /opt/sites/test/wwwroot/mc-automanaged/newtest/mc-snippets/membercentral-proxy.conf;
            proxy_set_header X-MC-InternalRequest $isinternalrequest;
        }

        ################### CF HANDLER #####################
        location ~* (\.cfm|\.cfml|\.cfc|\.jsp|\.cfr|flex2gateway|messagebroker|flashservices|openamf)(.*)$ {
            proxy_pass  "http://MCDockerTestServer";
            include     /opt/sites/test/wwwroot/mc-automanaged/newtest/mc-snippets/membercentral-proxy.conf;
            proxy_set_header X-MC-InternalRequest $isinternalrequest;
        }

		location ~* ^/(assets|sitecomponents) {
            root "$rootfolder";
			rewrite (?i)^/assets/([A-Za-z0-9]+)/([A-Za-z0-9]+)/(galleries|storeimages|usercss|userimages|blogimages)/(.*) /userassets/$1/$2/$3/$4;
			rewrite (?i)^/assets/common/(adsystem|groupimages|invoices|reports|searchads)(.*) /userassets/common/$1$2;
			rewrite (?i)^/assets/ts/ts/(.*) /assets/tsjn/tsjn/$1;
			
            ################### CF HANDLER #####################
            location ~* (\.cfm|\.cfml|\.cfc|\.jsp|\.cfr|flex2gateway|messagebroker|flashservices|openamf)(.*)$ {
                proxy_pass  "http://MCDockerTestServer";
                include     /opt/sites/test/wwwroot/mc-automanaged/newtest/mc-snippets/membercentral-proxy.conf;
                proxy_set_header X-MC-InternalRequest $isinternalrequest;
            }
			location ~* ^/(assets|sitecomponents) {
                proxy_set_header Host "mcassets-newtest.local.membercentral.com";
                include     /opt/sites/test/wwwroot/mc-automanaged/newtest/mc-snippets/membercentral-proxy.conf;
                proxy_pass "http://MCAssetsTest";
			}
		}

		location ~* ^/userassets {
			rewrite (?i)^/userassets/([A-Za-z0-9]+)/([A-Za-z0-9]+)/(memberphotos|memberphotosth)/(.*) /userassets/$1/$3/$4;
			rewrite (?i)^/userassets/ts/ts/(.*) /userassets/tsjn/tsjn/$1;
			
            ################### CF HANDLER #####################
            location ~* (\.cfm|\.cfml|\.cfc|\.jsp|\.cfr|flex2gateway|messagebroker|flashservices|openamf)(.*)$ {
                proxy_pass  "http://MCDockerTestServer";
                include     /opt/sites/test/wwwroot/mc-automanaged/newtest/mc-snippets/membercentral-proxy.conf;
                proxy_set_header X-MC-InternalRequest $isinternalrequest;
            }
			location ~* ^/userassets {
                proxy_set_header Host "mcassets-newtest.local.membercentral.com";
                include     /opt/sites/test/wwwroot/mc-automanaged/newtest/mc-snippets/membercentral-proxy.conf;
                proxy_pass "http://MCAssetsTest";
			}
		}

     	location /temp {
            alias   /opt/sites/test/wwwroot/membercentral/temp; 
        }

     	location /app/backendroot/temp {
            alias   /opt/sites/test/backendroot/temp; 
        }	

     	location /app/wwwroot/sitedocuments {
            internal;
            alias   /opt/sites/test/wwwroot/siteDocuments; 
        }

     	location /app/tlasites {
            internal;
            alias   /opt/sites/test/tlasites; 
        }

     	location /app/imageraid {
            internal;
            alias   /opt/sites/test/imageraid; 
        }

     	location /app/wwwroot/temp {
            internal;
            alias   /opt/sites/test/wwwroot/temp; 
        }

     	location /app/wwwroot/membercentral/temp {
            internal;
            alias   /opt/sites/test/wwwroot/membercentral/temp; 
        }

    }

    ################### REWRITE: SES RULES #####################
    # Rewrite for URL Mappings
    # If you don't use SES urls you could do something like this
    # location ~ \.(cfm|cfml|cfc)(.*)$ {
    location @rewrite {
        rewrite (?i)^/(.*)? /index.cfm/$request_uri last;
        rewrite (?i)^ /index.cfm last;
    }

    ################### SECURITY BLOCKING #####################
    # Block root box.json and server.json
    location ~* /(box.json|server.json){
        access_log off; log_not_found off;
        allow 127.0.0.1;
        #deny all;
    }

    # Lucee Admin/Doc blocking
    location ~* /lucee/(admin|doc){
        access_log off; log_not_found off;
        allow 127.0.0.1;
        #deny      all;    
        proxy_pass  "http://MCDockerTestServer";
        include     /opt/sites/test/wwwroot/mc-automanaged/newtest/mc-snippets/membercentral-proxy.conf;
    }

    # Lucee Server Context blocking
    location ~* /lucee\-server/{
        access_log off; log_not_found off;
        allow 127.0.0.1;
        #deny all;
        proxy_pass  "http://MCDockerTestServer";
        include     /opt/sites/test/wwwroot/mc-automanaged/newtest/mc-snippets/membercentral-proxy.conf;
    }

    ################### CF HANDLER #####################
    location ~* (\.cfm|\.cfml|\.cfc|\.jsp|\.cfr|flex2gateway|messagebroker|flashservices|openamf)(.*)$ {
        proxy_pass  "http://MCDockerTestServer";
        include     /opt/sites/test/wwwroot/mc-automanaged/newtest/mc-snippets/membercentral-proxy.conf;
        proxy_set_header X-MC-InternalRequest $isinternalrequest;
    }

    location ~* ^/robots.txt$ {
        alias "/opt/sites/test/WWWRoot/mc-automanaged/newtest/sitemaps/$sitecode/robots.txt";
    }

    location ~* ^/sitemap(.*?\.xml(\.gz)?)$ {
         alias "/opt/sites/test/WWWRoot/mc-automanaged/newtest/sitemaps/$sitecode/sitemap$1";
    }
    