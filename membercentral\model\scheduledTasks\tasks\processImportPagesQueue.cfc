<cfcomponent output="false" extends="model.scheduledTasks.scheduledTasks">

	<cffunction name="runTask" access="public" returntype="void" output="false">
		<cfargument name="strTask" type="struct" required="true">
		<cfargument name="rc" type="struct" required="true">
		
		<cfset var local = structnew()>

		<cfsetting requesttimeout="600">

		<cfset local.itemCount = getQueueItemCount()>

		<cfif local.itemCount GT 0>
			<cfset local.success = processQueue()>
			<cfif NOT local.success>
				<cfthrow message="Error running processQueue()">
			</cfif>
		</cfif>

		<cfset local.historyID = (arguments.strTask.historyID ?: 0)>
		<cfset addBatchIdentifierToHistory(historyID=local.historyID, batchIdentifier="", itemCount=local.itemCount)>
	</cffunction>

	<cffunction name="processQueue" access="private" output="false" returntype="boolean">
		<cfset var local = structnew()>
		<cfset local.success = true>
		<cftry>
			<!--- ----------------------------------------------------------------------------------- --->
			<!--- post processing: process any notifications for itemGroupUIDs that are readyToNotify --->
			<!--- ----------------------------------------------------------------------------------- --->
			<cfstoredproc datasource="#application.dsn.platformQueue.dsn#" procedure="queue_importPages_grabForNotification">
				<cfprocresult name="local.qryNotifications" resultset="1">
			</cfstoredproc>

			<cfif local.qryNotifications.recordcount>
				<cfset local.tdStyle = "font-family:Verdana, Arial, Helvetica, sans-serif;font-size:9pt;color:##333;padding:2px;">
				<cfset local.thStyle = "font-family:Verdana, Arial, Helvetica, sans-serif;font-size:9pt;color:##333;border-bottom:1px solid ##333;padding:2px;">
				<cfset local.pageStyle = "width:600px;padding:1px 1px 1px 1px;font-family:Verdana, Arial, Helvetica, sans-serif;font-size:9pt;color:##333;">
				<cfset local.errStyle = "color:##f00;font-weight:bold;">

				<!--- which sites require alias updates --->
				<cfquery name="local.qryAliasUpdateSites" datasource="#application.dsn.platformQueue.dsn#">
					SET NOCOUNT ON;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					SELECT DISTINCT siteID
					FROM dbo.queue_importPages
					WHERE itemGroupUID IN (<cfqueryparam cfsqltype="CF_SQL_IDSTAMP" list="true" value="#valueList(local.qryNotifications.itemGroupUID)#">);

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				</cfquery>

				<cfif local.qryAliasUpdateSites.recordCount>
					<cfset local.objPlatformRewrites = CreateObject("component","model.system.platform.rewrites")>
					<cfloop query="local.qryAliasUpdateSites">
						<cfset local.objPlatformRewrites.updateAliases(siteID=local.qryAliasUpdateSites.siteID, requestNGINXReload=true)>
					</cfloop>
				</cfif>

				<cfoutput query="local.qryNotifications" group="itemGroupUID">
					<cftry>
						<!--- prep and send email --->
						<cfset local.thisReportEmail = local.qryNotifications.reportEmail>
						<cfset local.thisSiteName = local.qryNotifications.siteName>
						<cfset local.thisSiteCode = local.qryNotifications.siteCode>

						<cfif len(local.thisReportEmail)>
							
							<cfset local.thisEmailContentDetail = "<ul>">
							<cfoutput>
								<cfset local.thisEmailContentDetail = local.thisEmailContentDetail & "<li>#local.qryNotifications.pageName# - #local.qryNotifications.sectionName#</li>">
							</cfoutput>
							<cfset local.thisEmailContentDetail = local.thisEmailContentDetail & "</ul>">

							<cfsavecontent variable="local.thisEmailContent">
								<div style="#local.pageStyle#">
									<div>
										We have completed processing your pages import.<br/><br/>
										#local.thisEmailContentDetail#
									</div>
									<br/>
									Requested by: #local.qryNotifications.firstname# #local.qryNotifications.lastname# (#local.qryNotifications.membernumber#) <br/>
								</div>
							</cfsavecontent>

							<cfscript>
								local.arrEmailTo = [];
								
								local.toEmailArr = listToArray(local.thisReportEmail,';');
								for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
									local.arrEmailTo.append({ name:'', email:local.toEmailArr[local.i] });
								}
							</cfscript>
								
							<cfset local.mc_siteinfo = application.objSiteInfo.getSiteInfo(local.thisSiteCode)>
							<cfset local.strEmailResult = application.objEmailWrapper.sendMailESQ(
								emailfrom={ name='MemberCentral', email='<EMAIL>' },
								emailto=local.arrEmailTo,
								emailreplyto="<EMAIL>",
								emailsubject="#local.thisSiteName# Import Pages Report" ,
								emailtitle="#local.thisSiteName# Import Pages Report",
								emailhtmlcontent=local.thisEmailContent,
								emailAttachments=[],
								siteID=local.mc_siteinfo.siteID,
								memberID=local.qryNotifications.runByMemberID,
								messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="SCHEDTASK"),
								sendingSiteResourceID=local.mc_siteinfo.siteSiteResourceID
							)>

						</cfif>

						<!--- update status --->
						<cfquery name="local.updateStatusInMass" datasource="#application.dsn.platformQueue.dsn#">
							set nocount on;
	
							declare @newstatus int;
							select @newstatus = qs.queueStatusID 
								from dbo.tblQueueStatuses as qs
								inner join dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
								where qt.queueType = 'importPages'
								and qs.queueStatus = 'done';
							
							IF @newstatus is not null
								update dbo.queue_importPages
								set statusID = @newstatus,
									dateUpdated = getdate()
								where itemGroupUID = <cfqueryparam cfsqltype="CF_SQL_IDSTAMP" value="#local.qryNotifications.itemGroupUID#">;
						</cfquery>

					<cfcatch type="Any">
						<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
					</cfcatch>
					</cftry>
				</cfoutput>
			</cfif>

			<!--- -------------------------------------------------------- --->
			<!--- post processing - delete any itemGroupUIDs that are done --->
			<!--- -------------------------------------------------------- --->
			<cfstoredproc datasource="#application.dsn.platformQueue.dsn#" procedure="queue_importPages_clearDone">
			</cfstoredproc>

			<cfset local.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset local.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.success>
	</cffunction>

	<cffunction name="getQueueItemCount" access="private" output="false" returntype="numeric">
		<cfset var qryQueueItems = "">

		<cfquery name="qryQueueItems" datasource="#application.dsn.platformQueue.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT COUNT(itemID) as itemCount
			FROM dbo.queue_importPages;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryQueueItems.itemCount>
	</cffunction>

</cfcomponent>