ALTER PROC dbo.tr_addMissingPaymentTransaction
@itemCount int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	-- this identifies payment history missing payment transactions
	-- look for anything older than 5 min but less than 3 hours.
	-- older than 5 min so we dont get entries currently being written; 3 hours because this runs every hour and in case it fails up to 2 times.

	declare @datePaidOldest datetime = dateadd(HOUR,-3,getdate()), 
		@datePaidNewest datetime = dateadd(MINUTE,-5,getdate()),
		@historyID int, @payerMemberID int, @orgID int, @siteID int, @paymentDate datetime, 
		@depositDate varchar(10), @profileid int, @profileCode varchar(20), @payGL int, @statsSessionID int, 
		@detail varchar(300), @amount decimal(18,2), @glName varchar(200), @batchCode varchar(40), 
		@batchName varchar(400), @batchID int, @transactionID int, @gatewayType varchar(30), 
		@gatewaytxnid varchar(40), @paymentMethod varchar(20), @externalBatchID varchar(40), @historyHandled bit, 
		@responsereasoncode varchar(3);
	
	IF OBJECT_ID('tempdb..#tblPayHistory') IS NOT NULL 
		DROP TABLE #tblPayHistory;
	CREATE TABLE #tblPayHistory (historyID int not null PRIMARY KEY);

	-- identify payment history missing payment transactions
	INSERT INTO #tblPayHistory (historyID)
	select historyID
	from dbo.tr_paymentHistory
	where datePaid between @datePaidOldest and @datePaidNewest
	and paymentType = 'payment'
	and isSuccess = 1
	and paymentID is null;

	SET @itemCount = @@ROWCOUNT;

	select @historyID = min(historyID) from #tblPayHistory;
	while @historyID is not null begin

		select @payerMemberID = null, @orgID = null, @siteID = null, @paymentDate = null, @depositDate = null, 
			@profileid = null, @profileCode = null, @statsSessionID = null, @payGL = null, @detail = null, 
			@amount = null, @transactionID = null, @gatewayType = null, @historyHandled = 0, @batchID = null,
			@batchCode = null, @batchName = null, @responsereasoncode = null, @glName = null;

		-- pull payment info
		SELECT @payerMemberID=ph.payerMemberID, @orgID=s.orgID, @siteID=s.siteID, @paymentDate=ph.datePaid, 
			@depositDate=convert(varchar(10),ph.datePaid,101), @profileid=mp.profileID,
			@profileCode=mp.profileCode, @statsSessionID=ph.statsSessionID, @gatewayType=mg.gatewayType, 
			@payGL=ph.gatewayResponse.value('(/response/glaccountid)[1]','int'),
			@detail=ph.gatewayResponse.value('(/response/transactiondetail)[1]','varchar(300)'),
			@amount=ph.paymentInfo.value('(/payment/args/x_amount)[1]','decimal(14,2)'),
			@responsereasoncode=ph.gatewayResponse.value('(/response/responsereasoncode)[1]','varchar(3)')
		from dbo.tr_paymentHistory as ph
		inner join dbo.mp_profiles as mp on mp.profileID = ph.profileID
		inner join dbo.mp_gateways as mg on mg.gatewayID = mp.gatewayID
		inner join dbo.sites as s on s.siteID = mp.siteID
		where ph.historyID = @historyID;

		IF @historyHandled = 0 AND @gatewayType = 'OfflineCash' begin
			BEGIN TRY
			BEGIN TRAN;
				-- put on pending payments batch
				select @batchID = batchID
				from dbo.tr_batches
				where orgID = @orgID
				and statusID = 1
				and batchCode = 'PENDINGPAYMENTS'
				and isSystemCreated = 1;

				-- add payment transaction
				EXEC dbo.tr_createTransaction_payment @ownedByOrgID=@orgID, @recordedOnSiteID=@siteID, @assignedToMemberID=@payerMemberID, 
					@recordedByMemberID=@payerMemberID, @statsSessionID=@statsSessionID, @status='Pending', @detail=@detail, @amount=@amount, 
					@transactionDate=@paymentDate, @debitGLAccountID=@payGL, @profileID=@profileid, @historyID=@historyID, @batchid=@batchID, 
					@offeredPaymentFee=0, @isApplePay=0, @isGooglePay=0, @transactionID=@transactionID OUTPUT;
				if @transactionID is null
					RAISERROR('Could not create payment.',16,1);

				-- update payment with actual date
				update dbo.tr_transactions 
				set dateRecorded = @paymentDate 
				where transactionID = @transactionID;

				-- create trans alert
				INSERT INTO dbo.tr_transactionAlerts (orgID, memberID, transactionID, message)
				VALUES (@orgID, @payerMemberID, @transactionID, left('"' + @detail + '" was not recorded properly at the time of payment. It was later recorded as a pending payment that needs to be accepted.',200));
			COMMIT TRAN;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=0, @email=1;
			END CATCH

			set @historyHandled = 1;
		end

		IF @historyHandled = 0 AND @gatewayType = 'BankDraft' begin
			BEGIN TRY
			BEGIN TRAN;
				-- determine batch and create one if necessary
				set @batchCode = @responsereasoncode + '_' + cast(@profileid as varchar(10)) + '_' + cast(@payGL as varchar(10));
				select @batchID = max(batchID)
					from dbo.tr_batches
					where orgID = @orgID
					and batchTypeID = 1
					and batchCode = @batchCode
					and statusID = 1;
			
				select @glName = AccountName from dbo.tr_GLAccounts where GLAccountID = @payGL;
				set @batchName = @responsereasoncode + ' ' + @profileCode + ' ' + @glName;
				
				IF @batchID is null 
					EXEC dbo.tr_createBatch @orgID=@orgID, @payProfileID=@profileid, @batchTypeID=1, @batchCode=@batchCode, 
						@batchName=@batchName, @controlAmt=0, @controlCount=0, @depositDate=@depositDate, 
						@isSystemCreated=1, @createdByMemberID=null, @batchID=@batchID OUTPUT;

				-- add payment transaction
				EXEC dbo.tr_createTransaction_payment @ownedByOrgID=@orgID, @recordedOnSiteID=@siteID, @assignedToMemberID=@payerMemberID, 
					@recordedByMemberID=@payerMemberID, @statsSessionID=@statsSessionID, @status='Active', @detail=@detail, @amount=@amount, 
					@transactionDate=@paymentDate, @debitGLAccountID=@payGL, @profileID=@profileid, @historyID=@historyID, @batchid=@batchID, 
					@offeredPaymentFee=0, @isApplePay=0, @isGooglePay=0, @transactionID=@transactionID OUTPUT;
				if @transactionID is null
					RAISERROR('Could not create payment.',16,1);

				-- update payment with actual date
				update dbo.tr_transactions 
				set dateRecorded = @paymentDate 
				where transactionID = @transactionID;

				-- create trans alert
				INSERT INTO dbo.tr_transactionAlerts (orgID, memberID, transactionID, message)
				VALUES (@orgID, @payerMemberID, @transactionID, left('"' + @detail + '" was not recorded properly at the time of payment. It was later recorded and included on batch "' +  @batchName + '".',200));
			COMMIT TRAN;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=0, @email=1;
			END CATCH

			set @historyHandled = 1;
		end

		IF @historyHandled = 0 AND @gatewayType IN ('AuthorizeCCCIM','SageCCCIM','AffiniPayCC') begin
			BEGIN TRY
			BEGIN TRAN;
				-- determine batch and create one if necessary
				set @batchCode = convert(char(8),@paymentDate,112) + '_' + cast(@profileid as varchar(10)) + '_' + cast(@payGL as varchar(10));
				select @batchID = batchID 
					from dbo.tr_batches 
					where orgID = @orgID
					and batchTypeID = 1 
					and batchCode = @batchCode
					and statusID = 1;

				select @glName = AccountName from dbo.tr_GLAccounts where GLAccountID = @payGL;
				set @batchName = convert(char(8),@paymentDate,112) + ' ' + @profileCode + ' ' + @glName;

				IF @batchID is null BEGIN
					SET @batchName = @batchName + ' - ' + cast(@historyID as varchar(10));
					SET @batchCode = @batchCode + '_' + cast(@historyID as varchar(10));
					EXEC dbo.tr_createBatch @orgID=@orgID, @payProfileID=@profileid, @batchTypeID=1, @batchCode=@batchCode, 
						@batchName=@batchName, @controlAmt=0, @controlCount=0, @depositDate=@depositDate, 
						@isSystemCreated=1, @createdByMemberID=null, @batchID=@batchID OUTPUT;
				END

				-- add payment transaction
				EXEC dbo.tr_createTransaction_payment @ownedByOrgID=@orgID, @recordedOnSiteID=@siteID, @assignedToMemberID=@payerMemberID, 
					@recordedByMemberID=@payerMemberID, @statsSessionID=@statsSessionID, @status='Active', @detail=@detail, @amount=@amount, 
					@transactionDate=@paymentDate, @debitGLAccountID=@payGL, @profileID=@profileid, @historyID=@historyID, @batchid=@batchID, 
					@offeredPaymentFee=0, @isApplePay=0, @isGooglePay=0, @transactionID=@transactionID OUTPUT;
				if @transactionID is null
					RAISERROR('Could not create payment.',16,1);

				-- update payment with actual date
				update dbo.tr_transactions 
				set dateRecorded = @paymentDate 
				where transactionID = @transactionID;

				-- create trans alert
				INSERT INTO dbo.tr_transactionAlerts (orgID, memberID, transactionID, message)
				VALUES (@orgID, @payerMemberID, @transactionID, left('"' + @detail + '" was not recorded properly at the time of payment. It was later recorded and included on batch "' +  @batchName + '".',200));
			COMMIT TRAN;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=0, @email=1;
			END CATCH

			set @historyHandled = 1;
		END

		IF @historyHandled = 0 BEGIN
			BEGIN TRY
				RAISERROR('%s Payment Gateway not coded to handle missing payments.',16,1,@gatewayType);
			END TRY
			BEGIN CATCH
				EXEC dbo.up_MCErrorHandler @raise=0, @email=1;
			END CATCH
		END

		select @historyID = min(historyID) from #tblPayHistory where historyID > @historyID;
	end

	IF OBJECT_ID('tempdb..#tblPayHistory') IS NOT NULL 
		DROP TABLE #tblPayHistory;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
