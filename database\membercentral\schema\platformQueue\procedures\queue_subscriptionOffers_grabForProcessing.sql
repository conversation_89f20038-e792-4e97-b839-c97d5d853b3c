ALTER PROC dbo.queue_subscriptionOffers_grabForProcessing
@batchSize int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @statusReady int, @statusGrabbed int;
	select @statusReady = qs.queueStatusID 
		from dbo.tblQueueStatuses as qs
		inner join dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'subscriptionOffers'
		and qs.queueStatus = 'readyToProcess';
	select @statusGrabbed = qs.queueStatusID 
		from dbo.tblQueueStatuses as qs
		inner join dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'subscriptionOffers'
		and qs.queueStatus = 'grabbedForProcessing';

	IF OBJECT_ID('tempdb..#tmpSubscribers') IS NOT NULL
		DROP TABLE #tmpSubscribers;
	IF OBJECT_ID('tempdb..#tmpReturn') IS NOT NULL
		DROP TABLE #tmpReturn;
	
	CREATE TABLE #tmpSubscribers (itemID int, emailTemplateID int, subscriberID int, emailMessageID int, overrideEmail varchar(200));

	-- dequeue
	update qi WITH (UPDLOCK, READPAST)
	set qi.statusID = @statusGrabbed,
		qi.dateUpdated = getdate()
		OUTPUT inserted.itemID, inserted.emailTemplateID, inserted.subscriberID, inserted.emailMessageID, inserted.overrideEmail 
		INTO #tmpSubscribers
	from dbo.queue_subscriptionOffers as qi
	inner join (
			select top(@BatchSize) qi2.itemID 
			from dbo.queue_subscriptionOffers as qi2
			where qi2.statusID = @statusReady
			order by qi2.queuePriority, qi2.itemID
		) as batch on batch.itemID = qi.itemID
	where qi.statusID = @statusReady;

	-- return subscriber information
	select qid.itemID, qid.subscriberID, qid.emailTemplateID, em.contentVersionID as templateContentVersionID, qid.emailMessageID, s.useRemoteLogin, 
		s.siteID, s.orgID, s.siteName, qid.overrideEmail
	into #tmpReturn	
	from #tmpSubscribers as qid
	inner join membercentral.dbo.et_emailTemplates as et on et.templateID = qid.emailTemplateID
	inner join membercentral.dbo.cms_categories as cat on cat.categoryID = et.categoryID
	inner join membercentral.dbo.cms_categoryTrees as ct on ct.categoryTreeID = cat.categoryTreeID
	inner join membercentral.dbo.sites as s on s.siteID = ct.siteID
	inner join platformMail.dbo.email_messages as em on em.siteID = s.siteID
		and em.messageID = qid.emailMessageID
	order by qid.itemID;

	-- return subscribers
	select itemID, subscriberID, emailTemplateID, templateContentVersionID, emailMessageID, useRemoteLogin, siteID, orgID, siteName, overrideEmail
	from #tmpReturn
	order by itemID;

	declare @regexMergeCode varchar(40);
	select @regexMergeCode = regexMergeCode from membercentral.dbo.fn_getServerSettings();
	
	-- return messageFields
	select distinct tmp.messageID as emailMessageID, MF.fieldID, MF.fieldName
	from (
		select distinct em.messageID, replace(em.messageWrapper,'@@rawcontent@@',cv.rawcontent) as messageToParse
		from platformMail.dbo.email_messages as em
		inner join membercentral.dbo.cms_contentVersions as cv on cv.contentVersionID = em.contentVersionID
		inner join #tmpReturn as tmpR on tmpR.siteID = em.siteID
			and tmpR.emailMessageID = em.messageID
	) as tmp
	cross apply membercentral.dbo.fn_RegexMatches(tmp.messageToParse,@regexMergeCode) as flds
	inner join platformMail.dbo.email_metadataFields as MF on MF.fieldName = left(flds.[Text],300) and MF.isMergeField = 1;

	IF OBJECT_ID('tempdb..#tmpSubscribers') IS NOT NULL
		DROP TABLE #tmpSubscribers;
	IF OBJECT_ID('tempdb..#tmpReturn') IS NOT NULL
		DROP TABLE #tmpReturn;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
