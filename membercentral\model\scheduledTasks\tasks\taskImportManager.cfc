<cfcomponent output="false" extends="model.scheduledTasks.scheduledTasks">

	<cffunction name="runTask" access="public" returntype="void" output="false">
		<cfargument name="strTask" type="struct" required="true">
		<cfargument name="rc" type="struct" required="true">
		
		<cfset var local = structnew()>

		<cfsetting requesttimeout="600">

		<cfset local.itemCount = getQueueItemCount()>

		<cfif local.itemCount GT 0>
			<cfset local.success = processQueue()>
			<cfif NOT local.success>
				<cfthrow message="Error running processQueue()">
			</cfif>
		</cfif>

		<cfset local.historyID = (arguments.strTask.historyID ?: 0)>
		<cfset addBatchIdentifierToHistory(historyID=local.historyID, batchIdentifier="", itemCount=local.itemCount)>
	</cffunction>

	<cffunction name="processQueue" access="private" output="false" returntype="boolean">
		<cfset var local = structnew()>
		<cfset local.success = true>
		<cftry>
			<!--- ----------------------------------------------------------------------------------- --->
			<!--- post processing: process any notifications for itemGroupUIDs that are readyToNotify --->
			<!--- ----------------------------------------------------------------------------------- --->
			<cfstoredproc datasource="#application.dsn.platformQueue.dsn#" procedure="queue_TaskImport_grabForNotification">
				<cfprocresult name="local.qryNotifications" resultset="1">
			</cfstoredproc>

			<cfif local.qryNotifications.recordcount>
				<cfset local.tdStyle = "font-family:Verdana, Arial, Helvetica, sans-serif;font-size:9pt;color:##333;padding:2px;">
				<cfset local.thStyle = "font-family:Verdana, Arial, Helvetica, sans-serif;font-size:9pt;color:##333;border-bottom:1px solid ##333;padding:2px;">
				<cfset local.pageStyle = "width:600px;padding:1px 1px 1px 1px;font-family:Verdana, Arial, Helvetica, sans-serif;font-size:9pt;color:##333;">
				<cfset local.errStyle = "color:##f00;font-weight:bold;">

				<cfoutput query="local.qryNotifications" group="itemGroupUID">
					<cftry>
						<!--- prep and send email --->
						<cfset local.thisReportEmail = local.qryNotifications.reportEmail>
						<cfset local.thisSiteName = local.qryNotifications.siteName>
						<cfset local.thisSiteCode = local.qryNotifications.siteCode>

						<cfif len(local.thisReportEmail)>
							
							<cfset local.thisEmailContentDetail = "<ul>">
							<cfoutput>
								<cfset local.thisEmailContentDetail = local.thisEmailContentDetail & "<li>#local.qryNotifications.prospectName# - #local.qryNotifications.project#</li>">
							</cfoutput>
							<cfset local.thisEmailContentDetail = local.thisEmailContentDetail & "</ul>">

							<cfsavecontent variable="local.thisEmailContent">
								<div style="#local.pageStyle#">
									<div>
										We have completed processing your tasks import.<br/><br/>
										#local.thisEmailContentDetail#
									</div>
									<br/>
									Requested by: #local.qryNotifications.firstname# #local.qryNotifications.lastname# (#local.qryNotifications.membernumber#) <br/>
								</div>
							</cfsavecontent>

							<cfset local.mc_siteinfo = application.objSiteInfo.getSiteInfo(local.thisSiteCode)>
							<cfset local.strEmailResult = application.objEmailWrapper.sendMailESQ(
								emailfrom={ name='MemberCentral', email='<EMAIL>' },
								emailto=[{name:"", email:local.thisReportEmail}],
								emailreplyto="<EMAIL>",
								emailsubject="#local.thisSiteName# Tasks Import Report" ,
								emailtitle="#local.thisSiteName# Tasks Import Report",
								emailhtmlcontent=local.thisEmailContent,
								emailAttachments=[],
								siteID=local.mc_siteinfo.siteID,
								memberID=local.qryNotifications.recordedByMemberID,
								messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="SCHEDTASK"),
								sendingSiteResourceID=local.mc_siteinfo.siteSiteResourceID
							)>
						</cfif>

						<!--- update status --->
						<cfquery name="local.updateStatusInMass" datasource="#application.dsn.platformQueue.dsn#">
							set nocount on;
		
							declare @newstatus int;
							select @newstatus = qs.queueStatusID 
								from dbo.tblQueueStatuses as qs
								inner join dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
								where qt.queueType = 'TaskImport'
								and qs.queueStatus = 'done';
							
							update qi
							set qi.queueStatusID = @newstatus,
								dateUpdated = getdate()
							from dbo.tblQueueItems as qi
							inner join dbo.tblQueueItemData as qid on qid.itemUID = qi.itemUID
							where qid.itemGroupUID = <cfqueryparam cfsqltype="cf_sql_idstamp" value="#local.qryNotifications.itemGroupUID#">;
						</cfquery>

					<cfcatch type="Any">
						<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
					</cfcatch>
					</cftry>

				</cfoutput>
			</cfif>

			<!--- -------------------------------------------------------- --->
			<!--- post processing - delete any itemGroupUIDs that are done --->
			<!--- -------------------------------------------------------- --->
			<cfstoredproc datasource="#application.dsn.platformQueue.dsn#" procedure="queue_TaskImport_clearDone">
			</cfstoredproc>

			<cfset local.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset local.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.success>
	</cffunction>

	<cffunction name="getQueueItemCount" access="private" output="false" returntype="numeric">
		<cfset var qryQueueItems = "">

		<cfquery name="qryQueueItems" datasource="#application.dsn.platformQueue.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select count(qi.itemUID) as itemCount
			from dbo.tblQueueItems as qi
			inner join dbo.tblQueueStatuses as qs on qs.queueStatusID = qi.queueStatusID
			inner join dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
			where qt.queueType = 'TaskImport';

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryQueueItems.itemCount>
	</cffunction>

</cfcomponent>