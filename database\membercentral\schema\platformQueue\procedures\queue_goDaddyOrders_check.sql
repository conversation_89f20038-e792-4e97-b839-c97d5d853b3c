ALTER PROC dbo.queue_goDaddyOrders_check

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @issueCount int, @timeToUse datetime, @queueTypeID int, @queueStatusID int, @nextQueueStatusID int,
		@errorSubject VARCHAR(400), @errorTitle VARCHAR(400);
	SELECT @queueTypeID = queueTypeID FROM dbo.tblQueueTypes WHERE queueType = 'goDaddyOrders';

	-- goDaddyOrders / processingItem autoreset to readyToProcess
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -10, GETDATE());
	SELECT @queueStatusID = queuestatusID from dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'processingItem';
	SELECT @nextQueueStatusID = queuestatusID from dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'readyToProcess';
	SELECT @issueCount = count(itemID) FROM dbo.queue_goDaddyOrders WHERE statusID = @queueStatusID and dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.queue_goDaddyOrders
		SET statusID = @nextQueueStatusID,
			dateUpdated = getdate()
		WHERE statusID = @queueStatusID
		AND dateUpdated < @timeToUse;

		EXEC membercentral.dbo.sched_resumeTask @name='Process GoDaddy Orders Queue', @engine='BERLinux';

		SET @errorTitle = 'goDaddyOrders Queue Issue';
		SET @errorSubject = 'goDaddyOrders queue moved items from processingItem to readyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- goDaddyOrders readyToProcess notify
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(hour, -4, GETDATE());
	SELECT @queueStatusID = queuestatusID from dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'readyToProcess';
	SELECT @issueCount = count(itemID) FROM dbo.queue_goDaddyOrders WHERE statusID = @queueStatusID and dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		SET @errorTitle = 'goDaddyOrders Queue Issue';
		SET @errorSubject = 'goDaddyOrders queue has items in readyToProcess with dateUpdated older than 4 hours.';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
