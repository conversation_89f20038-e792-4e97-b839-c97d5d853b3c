ALTER PROC dbo.queue_referralScheduledReports_grabForProcessing
@scheduledReportTypeID int
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @batchSize int;
	set @batchSize = 75;

	declare @statusReady int, @statusGrabbed int;

	select @statusReady = qs.queueStatusID 
		from dbo.tblQueueStatuses as qs
		inner join dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'referralScheduledReports'
		and qs.queueStatus = 'readyToProcess';
	select @statusGrabbed = qs.queueStatusID 
		from dbo.tblQueueStatuses as qs
		inner join dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'referralScheduledReports'
		and qs.queueStatus = 'grabbedForProcessing';

	IF OBJECT_ID('tempdb..#tmpReferralScheduledReports') IS NOT NULL 
		DROP TABLE #tmpReferralScheduledReports;
	CREATE TABLE #tmpReferralScheduledReports(itemID int);

	-- dequeue in order of dateAdded. get @batchsize items
	UPDATE qi WITH (UPDLOCK, READPAST)
	SET qi.statusID = @statusGrabbed,
		qi.dateUpdated = getdate()
		OUTPUT inserted.itemID INTO #tmpReferralScheduledReports
	FROM dbo.queue_referralScheduledReports as qi
	INNER JOIN (
		SELECT top(@BatchSize) qi2.itemID 
		FROM dbo.queue_referralScheduledReports as qi2
		WHERE qi2.statusID = @statusReady
		AND qi2.scheduledReportTypeID = @scheduledReportTypeID
		ORDER BY qi2.dateAdded, qi2.itemID
	) as batch on batch.itemID = qi.itemID
	WHERE qi.statusID = @statusReady;

	-- final data
	select qidd.itemID, qidd.itemGroupUID, qidd.referralID,	qidd.memberID, qidd.memberName, qidd.templateID, 
		qidd.clientTemplateID, qidd.template, qidd.siteID, qidd.scheduleJobID, qidd.staffEmail, qidd.fromEmail, 
		s.siteCode, s.siteName, o.orgID, o.orgCode, oi.organizationName as orgName, fds.statusCode as feeDiscrepancyStatusCode
	from #tmpReferralScheduledReports as qid
	inner join dbo.queue_referralScheduledReports as qidd on qidd.itemID = qid.itemID
	inner join membercentral.dbo.sites s ON s.siteID = qidd.siteID
	INNER join membercentral.dbo.organizations o ON o.orgID = s.orgID
	inner join membercentral.dbo.orgIdentities as oi on oi.orgID = o.orgID and oi.orgIdentityID = o.defaultOrgIdentityID
	left outer join membercentral.dbo.ref_feeDiscrepancyStatuses as fds on fds.feeDiscrepancyStatusID = qidd.feeDiscrepancyStatusID
	order by qid.itemID;

	IF OBJECT_ID('tempdb..#tmpReferralScheduledReports') IS NOT NULL 
		DROP TABLE #tmpReferralScheduledReports;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
