ALTER PROC dbo.queue_EventCertificate_grabForProcessing

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @batchSize int, @statusReady int, @statusGrabbed int;
	set @batchSize = 50;

	select @statusReady = qs.queueStatusID 
	from dbo.tblQueueStatuses as qs
	inner join dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
	where qt.queueType = 'eventCertificate'
	and qs.queueStatus = 'readyToProcess';

	select @statusGrabbed = qs.queueStatusID 
	from dbo.tblQueueStatuses as qs
	inner join dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
	where qt.queueType = 'eventCertificate'
	and qs.queueStatus = 'grabbedForProcessing';

	IF OBJECT_ID('tempdb..#tmpEventCertificates') IS NOT NULL 
		DROP TABLE #tmpEventCertificates;
	CREATE TABLE #tmpEventCertificates(itemID int PRIMARY KEY);

	-- dequeue in order of dateAdded. get @batchsize payments
	update qi WITH (UPDLOCK, READPAST)
	set qi.statusID = @statusGrabbed,
		qi.dateUpdated = getdate()
		OUTPUT inserted.itemID INTO #tmpEventCertificates
	from dbo.queue_eventCertificate as qi
	inner join (
			select top(@BatchSize) qi2.itemID 
			from dbo.queue_eventCertificate as qi2
			where qi2.statusID = @statusReady
			order by qi2.dateAdded, qi2.itemID
		) as batch on batch.itemID = qi.itemID
	where qi.statusID = @statusReady;

	-- final data
	select qidd.itemID, qidd.itemGroupUID, qidd.recordedByMemberID, qidd.siteID, qidd.eventID, 
		qidd.registrantID, qidd.isProcessed, qidd.errMessage, qidd.recipientID, mrh.messageID
	from #tmpEventCertificates as qid
	inner join dbo.queue_eventCertificate as qidd on qidd.itemID = qid.itemID
	inner join platformMail.dbo.email_messageRecipientHistory mrh 
		on mrh.siteID = qidd.siteID 
		and mrh.recipientID = qidd.recipientID
	order by qidd.itemID;

	IF OBJECT_ID('tempdb..#tmpEventCertificates') IS NOT NULL 
		DROP TABLE #tmpEventCertificates;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
END CATCH
GO
