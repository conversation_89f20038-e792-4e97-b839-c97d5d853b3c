ALTER PROC dbo.queue_EventCertificate_check

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @issueCount INT, @timeToUse DATETIME, @queueTypeID INT, @queueStatusID INT, @nextQueueStatusID INT, 
		@itemAsStr VARCHAR(60), @xmlMessage xml, @errorSubject VARCHAR(400), @errorTitle VARCHAR(400);
	SELECT @queueTypeID = queueTypeID FROM dbo.tblQueueTypes WHERE queueType = 'EventCertificate';

	-- EventCertificate / grabbedForProcessing autoreset to readyToProcess
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -30, GETDATE());
	SELECT @queueStatusID = queuestatusID FROM dbo.tblQueueStatuses WHERE queueTypeID = @queueTypeID AND queueStatus = 'grabbedForProcessing';
	SELECT @nextQueueStatusID = queuestatusID FROM dbo.tblQueueStatuses WHERE queueTypeID = @queueTypeID AND queueStatus = 'readyToProcess';
	SELECT @issueCount = count(itemID) FROM dbo.queue_eventCertificate WHERE statusID = @queueStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.queue_eventCertificate
		SET statusID = @nextQueueStatusID, 
			dateUpdated = GETDATE()
		WHERE statusID = @queueStatusID 
		AND dateUpdated < @timeToUse;

		SET @errorTitle = 'EventCertificate Queue Issue';
		SET @errorSubject = 'EventCertificate queue moved items from grabbedForProcessing to readyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- EventCertificate / processingItem autoreset to readyToProcess
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -10, GETDATE());
	SELECT @queueStatusID = queuestatusID FROM dbo.tblQueueStatuses WHERE queueTypeID = @queueTypeID AND queueStatus = 'processingItem';
	SELECT @nextQueueStatusID = queuestatusID FROM dbo.tblQueueStatuses WHERE queueTypeID = @queueTypeID AND queueStatus = 'readyToProcess';
	SELECT @issueCount = count(itemID) FROM dbo.queue_eventCertificate WHERE statusID = @queueStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.queue_eventCertificate
		SET statusID = @nextQueueStatusID, 
			dateUpdated = GETDATE()
		WHERE statusID = @queueStatusID 
		AND dateUpdated < @timeToUse;

		SET @errorTitle = 'EventCertificate Queue Issue';
		SET @errorSubject = 'EventCertificate queue moved items from processingItem to readyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- EventCertificate AND email sending queue
	-- This code is to support sending thousands of event certs.
	-- The email sending queue will auto cancel messages that are inserting after 10 min, thinking they are stuck.
	-- So here, we will update email sending queue recipients tied to event certs that have not yet been processed to keep them current.
	IF OBJECT_ID('tempdb..#tmpRecipients') IS NOT NULL 
		DROP TABLE #tmpRecipients;
	CREATE TABLE #tmpRecipients (recipientID INT PRIMARY KEY);

	INSERT INTO #tmpRecipients (recipientID)
	SELECT qi.recipientID
	from dbo.queue_eventCertificate as qi
	inner join dbo.tblQueueStatuses AS qs on qs.queueTypeID = @queueTypeID AND qs.queueStatusID = qi.statusID 
	WHERE qs.queueStatus in ('readyToProcess','grabbedForProcessing','processingItem')
	AND qi.recipientID IS NOT NULL;

	UPDATE mrh
	SET mrh.dateLastUpdated = GETDATE()
	FROM #tmpRecipients as tmp
	INNER JOIN platformMail.dbo.email_messageRecipientHistory as mrh on mrh.recipientID = tmp.recipientID;

	IF OBJECT_ID('tempdb..#tmpRecipients') IS NOT NULL 
		DROP TABLE #tmpRecipients;

	-- EventCertificate catchall
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(hour, -4, GETDATE())
	SELECT @issueCount = COUNT(itemID) FROM dbo.queue_eventCertificate WHERE dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		SET @errorTitle = 'EventCertificate Queue Issue';
		SET @errorSubject = 'EventCertificate queue has items last updated more than 4 hours ago';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
