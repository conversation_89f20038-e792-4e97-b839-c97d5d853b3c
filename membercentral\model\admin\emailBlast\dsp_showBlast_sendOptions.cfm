<cfsavecontent variable="local.headJS">
	<cfoutput>
		<script language="javascript">
			function toggleButtonBar(f) { top.$('button.sendOptsButtonBar').prop('disabled',!f); }
			function scheduleBlast(ind) {
				var arrReq = [];
				mca_hideAlert('err_scheduleBlast');
				
				var AFID=0;
				if ($("##radioBlastBarRecurringYes:checked").length && $("##advanceFormulas").val() > 0) AFID = $("##advanceFormulas").val();
				if ($('##blastUntil').val().trim() == 2 && !$('##blastEndRunDate').val().trim().length) arrReq[arrReq.length] = 'Enter the End Run Date.';
				
				if(arrReq.length){
					mca_showAlert('err_scheduleBlast', 'Enter the End Run Date.', true);
					return false;
				}
				
				if (ind==0) {
					var msg = 'Are you sure you want to cancel the schedule for this email blast now?';
					var fd = top.getBlastFormData();
					fd["emailDateScheduled"] = '';
					let errmsg = 'There was an error cancelling the email blast schedule. Contact us for assistance before attempting to schedule it again.';
					doScheduleBlast(fd, errmsg);

				} else {
					var ds = $('##emailDateScheduled').val();
					if (ds.length > 0) {
						var fd = top.getBlastFormData();
						fd["emailDateScheduled"] = ds;
						fd["afID"] = AFID;
						fd["endRunDate"] = $('##blastEndRunDate').val();
						let errmsg = 'There was an error scheduling the email blast. Contact us for assistance before attempting to schedule it again.';
						doScheduleBlast(fd, errmsg);
					} else {
						mca_showAlert('err_scheduleBlast', 'To <cfif len(local.qryBlastInfo.emailDateScheduled)>reschedule<cfelse>schedule</cfif> this Email Blast for future delivery, choose a date for delivery.', true);
					}
				}
			}
			function doScheduleBlast(fd, errmsg){
				$('##divShowScreenLoading').removeClass('d-none');
				toggleButtonBar(false);
				fd["postSaveAction"] = 'scheduleBlast';
				$('##divSubmitContainer').load('#this.link.saveBlast#', fd,
					function(response, status, xhr) {
						$('##divShowScreenLoading').addClass('d-none');
						if (status === 'error' || response.length === 0) mca_showAlert('err_scheduleBlast', errmsg, true);
						else top.onCompleteScheduleBlast();
						toggleButtonBar(true);
					}
				);
			}
			function sendBlast() {
				let btnSendBlast = top.$('##btnBlastBarSendBlast');
				mca_initConfirmButton(btnSendBlast, function(){
					doSendBlast();
				},'<i class="fa-solid fa-envelope"></i> Send Blast Now',null,'Please Wait...');
			}
			function doSendBlast() {
				var AFID=0;
				if ($("##radioBlastBarRecurringYes:checked").length && $("##advanceFormulas").val() > 0){
					AFID = $("##advanceFormulas").val();
				}
				toggleButtonBar(false);
				mca_hideAlert('err_scheduleBlast');
				$('##divShowScreenLoading').removeClass('d-none');
				window.setTimeout(function() {
					var fd = top.getBlastFormData();
					fd["postSaveAction"] = 'sendBlast';
					fd["afID"] = AFID;
					$('##divSubmitContainer').load('#this.link.saveBlast#', fd,
						function(r) {
							let response = JSON.parse(r);
							$('##divShowScreenLoading').addClass('d-none');
							if(response.success && response.success == true){
								if(response.msg.length){
									$('##divSubmitContainer').html('<div class="alert alert-success mt-1">'+response.msg+'</div>').removeClass('d-none');
									setTimeout("top.onCompleteScheduleBlast()", 1000);
								}
								else top.onCompleteScheduleBlast();
							}
							else {
								mca_showAlert('err_scheduleBlast', 'There was an error sending the email blast. Contact us for assistance before attempting to send it again.', true);
								toggleButtonBar(true);
							}
						}
					);
				}, 500);
			}
			function updateButtonBar() {
				top.$('##btnBlastBarSendBlast,##btnBlastBarScheduleBlast').addClass('d-none');
				var scheduling = $('input[name="radioBlastBarScheduling"]:checked').val();
				if (scheduling == 'N') top.$('##btnBlastBarSendBlast').removeClass('d-none');
				else top.$('##btnBlastBarScheduleBlast').removeClass('d-none');

				updateAdvanceDates();
			}
			function markAsScheduledBlast() {
				if ($('##radioBlastBarScheduleBlast:checked').length == 0) $('##radioBlastBarScheduleBlast').click();
				updateAdvanceDates();
			}
			function markAsRecurringBlast() {
				if ($('##radioBlastBarRecurringYes:checked').length == 0) $('##radioBlastBarRecurringYes').click();
				updateAdvanceDates();

			}
			async function updateAdvanceDates() {
				var AFID = $("##advanceFormulas").val();
				var d = moment(new Date()).format('M/D/YYYY');

				if ($('##radioBlastBarScheduleBlast:checked').length && $('##emailDateScheduled').val().length > 0) {
					d = moment($('##emailDateScheduled').val().replace(' - ',' ')).format('M/D/YYYY');
				}

				$('##nextBlastSendDate').html('<i class="fa-solid fa-circle-notch fa-spin"></i>');
				
				if(AFID > 0){
					if ($('##radioBlastBarSendBlast:checked').length) top.$('##btnBlastBarSendBlast').removeClass('d-none');
					if ($('##radioBlastBarScheduleBlast:checked').length) top.$('##btnBlastBarScheduleBlast').removeClass('d-none');

					var ad1 = await calculateNextAdvanceDate(d,AFID);
					var ad2 = await calculateNextAdvanceDate(ad1,AFID);
					var ad3 = await calculateNextAdvanceDate(ad2,AFID);
					
					var dateArray = [ad1, ad2, ad3];
					if($('##blastEndRunDate').val().length){
						var endRunDate = moment($('##blastEndRunDate').val().replace(' - ',' ')).toDate();
						
						dateArray = dateArray.filter(function(date) {
							return  moment(date).toDate() <= endRunDate;
						});
					}
					
					if(dateArray.length){
						var html = '<strong>Next ' + dateArray.length + ' recurring send dates:</strong> ';
						dateArray.forEach(function(date, index) {
							if (index > 0) {
								html += ', ';
							}
							html += moment(date).format('M/D/YYYY');
						});

						$('##nextBlastSendDate').html(html);
					} else {
						$('##nextBlastSendDate').html('');
					}
				}
				else {
					$('##nextBlastSendDate').html('');
				}
			}
			function calculateNextAdvanceDate(baseDate,AFID) {
				return new Promise(function(resolve, reject) {
					var getAFDateResult = function(r) {
						if (r.success && r.success.toLowerCase() == 'true') resolve(r.retdate);
						else reject('Unable to calculate date.');
					};
					let objParams = { baseDate:baseDate, afid:AFID };
					TS_AJX('ADMADVFORM','getAdvanceFormulaDateforAFID',objParams,getAFDateResult,getAFDateResult,10000,getAFDateResult);
				});
			}
			function checkMessageHasNoOptOutMergeTags() {
				var fd = top.getBlastFormData();
				if(fd.excludeOptOuts == 1){
					var checkResult = function(s) {
						if (s.success && s.success.toLowerCase() == 'true')
							$('##showManagePreferencesFooterMsg').toggleClass('d-none d-flex');
					};

					var objParams = { blastContent:fd.blastContent, footerContentID:Number(fd.footerContentID) };
					TS_AJX_SYNC('ADMINEMAILBLAST','checkMessageHasNoOptOutMergeTags',objParams,checkResult,checkResult,5000,checkResult);
				}
			}
			function cancelBlastSchedule() {
				scheduleBlast(0);
			}
			function previewBlastRecipients() {
				$('##adminwrapper').html(mca_getLoadingHTML());
				top.$('##MCModalLabel').text('Preview Recipients');
				top.$('##MCModalFooter').html('<button type="button" class="btn btn-sm btn-secondary" data-dismiss="modal">Close</button>').removeClass('d-flex');
				self.location.href = '#local.previewRecipientSummaryLink#';
			}

			$(function(){
				mca_setupDateTimePickerField('emailDateScheduled',0,0,30);
				mca_setupDateTimePickerField('blastEndRunDate','#dateTimeFormat(now(),'m/d/yyyy - h:nn tt')#','',5);
				mca_setupCalendarIcons('frmSendBlast');

				$('input[name="radioBlastBarScheduling"]').change(updateButtonBar).trigger('change');
				$('##advanceFormulas').change(markAsRecurringBlast);
				$('##emailDateScheduled,##blastEndRunDate').change(markAsScheduledBlast);
				$('input[name="radioBlastBarRecurring"]').change(updateAdvanceDates).trigger('change');
				updateButtonBar();
				checkMessageHasNoOptOutMergeTags();
				$('##blastUntil').on('change', function() {
					var selectedValue = $(this).val();
					if (selectedValue == '2') {
						$('##blastEndRunDateHolder').removeClass('d-none');					
					} else { 
						mca_clearDateRangeField('blastEndRunDate');
						$('##blastEndRunDateHolder').addClass('d-none');
					}
				});
			});
		</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.headJS)#">

<cfset local.schedulePerm = arguments.event.getValue('mc_adminToolInfo.myRights.scheduleAnyBlast') or (local.qryBlastInfo.createdByMemberID eq session.cfcuser.memberdata.memberid and arguments.event.getValue('mc_adminToolInfo.myRights.scheduleOwnBlast'))>
<cfoutput>
<div class="card card-box mt-2 mx-1">
	<div class="card-header bg-light">
		<div class="card-header--title font-weight-bold font-size-md">Email Blast Scheduling</div>
	</div>
	<div class="card-body p-3">
		<div id="err_scheduleBlast" class="alert alert-danger mb-2 d-none"></div>
		<cfif local.schedulePerm>
			<cfset local.currentScheduledDateTime = ""/>
			<cfset local.endRunDate = ""/>
			<cfif len(local.qryBlastInfo.emailDateScheduled)>
				<cfset local.currentScheduledDateTime = "#dateformat(local.qryBlastInfo.emailDateScheduled,"m/d/yyyy")# #timeformat(local.qryBlastInfo.emailDateScheduled,"h:mm TT")#" />			
			</cfif>
			<cfif len(local.qryBlastInfo.endRunDate)>
				<cfset local.endRunDate = "#dateformat(local.qryBlastInfo.endRunDate,"m/d/yyyy")# #timeformat(local.qryBlastInfo.endRunDate,"h:mm TT")#" />			
			</cfif>

			<form id="frmSendBlast" name="frmSendBlast">
				<div class="mb-3">You can send this Email Blast now or schedule a future time for us to send it for you.</div>
				<div class="px-3">
					<div class="form-input">
						<input type="radio" name="radioBlastBarScheduling" id="radioBlastBarSendBlast" value="N" class="form-input-control"<cfif not len(local.qryBlastInfo.emailDateScheduled) and local.totalMessagesToBeSent> checked</cfif><cfif not local.totalMessagesToBeSent> disabled</cfif>>
						<label for="radioBlastBarSendBlast" class="form-input-label">
							Send Blast Now<cfif local.totalMessagesToBeSent> to <span class="font-weight-bold">#local.totalMessagesToBeSent#</span> Recipient#local.totalMessagesToBeSent GT 1 ? 's' : ''#</cfif>
						</label>
						<cfif local.totalMessagesToBeSent>
							(<a href="##" class="border-bottom border-dark small" onclick="previewBlastRecipients();return false;">see details</a>)
						<cfelse>
							<span class="font-weight-bold text-danger">(No valid recipients found)</span>
						</cfif>
					</div>
					<div class="form-input ml-3 mt-1">
						<em>* Opted-Out recipients will be added and then removed from the mailing so that their records reflect the history of messages that they were opted out of.</p></em>
					</div>
					<div class="form-input mt-2">
						<input type="radio" name="radioBlastBarScheduling" id="radioBlastBarScheduleBlast" value="L" class="form-input-control"<cfif len(local.qryBlastInfo.emailDateScheduled) or NOT local.totalMessagesToBeSent> checked</cfif>>
						<div class="form-input-label d-inline-block">
							<div class="d-flex flex-row align-items-center">
								<label for="radioBlastBarScheduleBlast" class="text-nowrap mb-0"><cfif len(local.qryBlastInfo.emailDateScheduled)>Reschedule<cfelse>Schedule</cfif> this Email Blast to be sent on</label>
								<div class="input-group input-group-sm mx-1">
									<input type="text" name="emailDateScheduled" id="emailDateScheduled" value="#local.currentScheduledDateTime#" class="form-control form-control-sm dateControl" autocomplete="off">
									<div class="input-group-append">
										<span class="input-group-text cursor-pointer calendar-button" data-target="emailDateScheduled"><i class="fa-solid fa-calendar"></i></span>
									</div>
								</div>
								<span>Central</span>
							</div>
						</div>
					</div>
					<div class="form-input ml-3 mt-1">
						<em>* Message will be sent to records that match the filter criteria at the time of sending.</em>
					</div>
				</div>

				<cfif local.qryAdvanceFormulas.recordCount>
					<div class="mt-3 mb-3">Do you want this to be an automatically recurring email blast?</div>
					<div class="px-3">
						<div class="form-input">
							<input type="radio" name="radioBlastBarRecurring" id="radioBlastBarRecurringNo" value="0" class="form-input-control"<cfif not local.qryBlastInfo.afID> checked</cfif>>
							<label for="radioBlastBarRecurringNo" class="form-input-label">No, send this message one time.</label>
						</div>
						<div class="form-input mt-2">
							<input type="radio" name="radioBlastBarRecurring" id="radioBlastBarRecurringYes" value="1" class="form-input-control"<cfif local.qryBlastInfo.afID> checked</cfif>>
							<div class="form-input-label d-inline-block">
								<div class="d-flex flex-row align-items-center">
									<label for="radioBlastBarRecurringYes" class="text-nowrap mb-0">Yes, schedule recurring sends using this advancement formula:</label>									
								</div>
							</div>
						</div>
						<div class="form-row mt-1 ml-3">
							<div class="col-4">
								<div class="form-label-group">
									<select name="advanceFormulas" id="advanceFormulas" class="custom-select">
										<option value="0">Choose an advancement formula</option>
										<cfloop query="local.qryAdvanceFormulas">
											<option value="#local.qryAdvanceFormulas.AFID#" <cfif local.qryBlastInfo.afID eq local.qryAdvanceFormulas.afID>selected</cfif>>#local.qryAdvanceFormulas.afName#</option>
										</cfloop>
									</select>
									<label for="advanceFormulas">Advancement Formula</label>
								</div>
							</div>
							<div class="col-3 pl-3">
								<div class="form-label-group">
									<select name="blastUntil" id="blastUntil" class="custom-select">
										<option value="1">No End Date</option>
										<option value="2" <cfif len(local.endRunDate)>selected</cfif>>A Specific Date</option>
									</select>
									<label for="blastUntil">Until</label>
								</div>
							</div>
							<div class="col-5 pl-3 <cfif len(local.endRunDate) EQ 0>d-none</cfif>" id="blastEndRunDateHolder">
								<div class="form-label-group">
									<div class="input-group dateFieldHolder">
										<input type="text" name="blastEndRunDate" id="blastEndRunDate" value="#local.endRunDate#" class="form-control dateControl" autocomplete="off">
										<div class="input-group-append">
											<span class="input-group-text cursor-pointer calendar-button" data-target="blastEndRunDate"><i class="fa-solid fa-calendar"></i></span>
										</div>
										<label for="blastEndRunDate">End Run Date (CT)</label>
									</div>
								</div>
							</div>
						</div>
						<div class="form-input ml-3 mt-1" id="nextBlastSendDate">
							<em>* Message will be sent to records that match the filter criteria at the time of sending.</em>
						</div>
					</div>
				<cfelse>
					<input type="hidden" name="radioBlastBarRecurring" id="radioBlastBarRecurringNo" value="0" />
				</cfif>
				<div id="showManagePreferencesFooterMsg" class="mt-3 d-none align-items-center pl-2 align-content-center alert alert-info alert-dismissible fade show" role="alert">
					<span class="font-size-lg d-block d-40 ml-2 mr-3 text-center">
						<i class="fa-regular fa-circle-info"></i>
					</span>
					<span>
						<div>
							<p>The required opt-out / manage preferences link was not in the body or footer of your email.</p>

							<p>For your convenience, we're adding a basic "Manage Preferences" link at the very bottom. To remove our automatic link, click "Go Back and Edit" and add the [[emailOptOutStatement]] or [[emailOptOutURL]] 
							merge tags to your message.</p>
						</div>
						<button type="button" id="btnBlastBarPreviewMessage" class="btn btn-sm btn-secondary mt-2 d-block" onclick="top.closeModalAndShowPreview();">
							<span class="btn-wrapper--icon"><i class="fa-solid fa-eye"></i></span>
							<span class="btn-wrapper--label">See Preview</span>
						</button>
					</span>
				</div>
			</form>
		<cfelse>
			<div class="aler alert-danger">You have no permissions to perform this operation.</div>
		</cfif>
	</div>
</div>
<div id="divShowScreenLoading" class="d-none">
	<div class="mt-4 text-center">
		<div class="spinner-border" role="status"></div>
		<div class="font-weight-bold mt-2">Please wait while we process this.</div>
	</div>
</div>
<div id="divSubmitContainer" class="success d-none"></div>
</cfoutput>