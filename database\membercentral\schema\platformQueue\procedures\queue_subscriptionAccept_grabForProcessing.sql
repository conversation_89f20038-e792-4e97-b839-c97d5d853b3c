ALTER PROC dbo.queue_subscriptionAccept_grabForProcessing
@batchSize int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @statusReady int, @statusGrabbed int, @billedStatusID int;
	select @statusReady = qs.queueStatusID 
		from dbo.tblQueueStatuses as qs
		inner join dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'subscriptionAccept'
		and qs.queueStatus = 'readyToProcess';
	select @statusGrabbed = qs.queueStatusID 
		from dbo.tblQueueStatuses as qs
		inner join dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'subscriptionAccept'
		and qs.queueStatus = 'grabbedForProcessing';

	select @billedStatusID=statusID
	from membercentral.dbo.sub_statuses
	where statuscode = 'o';

	IF OBJECT_ID('tempdb..#tmpSubscribers') IS NOT NULL 
		DROP TABLE #tmpSubscribers;
	CREATE TABLE #tmpSubscribers (itemID int);

	-- dequeue
	UPDATE qi WITH (UPDLOCK, READPAST)
	SET qi.statusID = @statusGrabbed,
		qi.dateUpdated = getdate()
		OUTPUT inserted.itemID INTO #tmpSubscribers
	FROM dbo.queue_subscriptionAccept as qi
	INNER JOIN (
		SELECT top(@BatchSize) qi2.itemID 
		from dbo.queue_subscriptionAccept as qi2
		inner join membercentral.dbo.sub_subscribers as ss 
			on ss.subscriberID = qi2.subscriberID
			and ss.statusID = @billedStatusID
		WHERE qi2.statusID = @statusReady
		ORDER BY qi2.queuePriority, qi2.itemID
	) as batch on batch.itemID = qi.itemID
	WHERE qi.statusID = @statusReady;

	-- return subscriber information
	select qid.itemID, qid.subscriberID, subs.subscriptionID, s.siteID, s.sitecode, s.orgID, qid.recordedByMemberID, m.memberID, qid.merchantProfileID, 
		mp.gatewayID, mp.profileCode, g.gatewayType, qid.memberPayProfileID, qid.checkNumber, qid.batchID, qid.paymentDate, qid.amountToCharge, 
		qid.overridePendingPayment
	from #tmpSubscribers as tmp
	inner join dbo.queue_subscriptionAccept as qid on qid.itemID = tmp.itemID
	inner join membercentral.dbo.sub_subscribers as ss on ss.subscriberID = qid.subscriberID
	inner join membercentral.dbo.sub_subscriptions as subs on subs.subscriptionID = ss.subscriptionID
	inner join membercentral.dbo.sub_types as t on t.typeID = subs.typeID
	inner join membercentral.dbo.sites as s on s.siteID = t.siteID
	inner join membercentral.dbo.ams_members as m on m.memberID = ss.memberID
	left outer join memberCentral.dbo.mp_profiles as mp on mp.profileID = qid.merchantProfileID
	left outer join memberCentral.dbo.mp_gateways as g on g.gatewayID = mp.gatewayID
	order by qid.itemID;

	IF OBJECT_ID('tempdb..#tmpSubscribers') IS NOT NULL 
		DROP TABLE #tmpSubscribers;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
