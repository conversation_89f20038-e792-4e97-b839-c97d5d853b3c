<cfsavecontent variable="local.settingsJS">
	<cfoutput>
	<script language="JavaScript">
		<cfif local.hasManageSWFormRights>
			var #toScript(local.SWFormsListLink,'link_SWFormsList')#;
			var #toScript(local.editFormLink,'link_editSWForm')#;
		</cfif>
		function saveSWLProgramSettings(callback) {
			mca_hideAlert('err_settings');
			var saveResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true'){
					<cfif local.hasEditSWLProgramAllRights>
						swl_showDownloadAllRegistrantsForIsNATLE = $("##isNATLE:checked").length;
					</cfif>
					$('##frmSWLProgramSettings,##divSWProgramSettingsSaveLoading').toggle();
					if(!$("##program-settings .card-header:first ##saveResponse").length)
						$("##program-settings .card-header:first .card-header--title").after('<span id="saveResponse"></span>');
					$('##program-settings .card-header:first ##saveResponse').html('<span class="badge badge-success">SAVED SUCCESSFULLY</span>').addClass('text-success').show().fadeOut(10000);
					if(programAdded)
						$('##nextButton').prop('disabled',false);
					else
						$('##program-settings .card-footer .save-button').prop('disabled',false);
					if (callback) {
						callback();
					}
				} else {
					var arrReq = [];
					arrReq.push(r.err && r.err.length ? r.err : 'We were unable to save sw program settings.');
					$('##err_settings').html(arrReq.join('<br/>')).removeClass('d-none');
					$('html,body').animate({scrollTop: $('##err_settings').offset().top-120},500);
					if(programAdded)
						$('##nextButton').prop('disabled',false);
					else
						$('##program-settings .card-footer .save-button').prop('disabled',false);
				}
			};

			var arrReq = [];
			var emailRegEx = new RegExp("#application.regEx.email#", "i");

			var customEmail = ($('##customEmail').val() || '').trim();
			if ((customEmail.length > 0 && !(emailRegEx.test(customEmail))) 
			|| $("##includeConnectionInstructionCustomEmail:checked").length == 1 &&  !(emailRegEx.test(customEmail))) arrReq.push('Enter a valid custom email address.');
			
			
			if($("##includeConnectionInstructionCustomText:checked").length == 1 && $('##customText').val().trim().length == 0){
				arrReq.push('Enter custom text for At the time of registration.');
			}else if ($("##includeConnectionInstructionCustomText:checked").length == 1 && $('##customText').length && $('##customText').val().trim().length > 1000 ){
				arrReq.push('Enter a custom text that is less than or equal to 1000 characters.');
			}

			if($("##enableCustomField:checked").length == 1 && $('##customFieldHolder .dataTable .dataTables_empty').length > 0){
				arrReq.push('Add custom field for At the time of registration.');
			}

			if($("##preTestRequired:checked").length == 1 && $('##SW_pretest_FormsTable .dataTables_empty').length > 0){
				arrReq.push('Select pre-test for Before webinar begins.');
			}
			if($("##examRequired:checked").length == 1 && $('##SW_posttest_FormsTable .dataTables_empty').length > 0){
				arrReq.push('Select exam for After webinar ends.');
			}
			if($("##evaluationRequired:checked").length == 1 && $('##SW_evaluation_FormsTable .dataTables_empty').length > 0){
				arrReq.push('Select evaluation for After webinar ends.');
			}
			
			if (arrReq.length) {
				$('##err_settings').html(arrReq.join('<br/>')).removeClass('d-none');
				$('html,body').animate({scrollTop: $('##err_settings').offset().top-120},500);
				if(programAdded)
					$('##nextButton').prop('disabled',false);
				else
					$('##program-settings .card-footer .save-button').prop('disabled',false);
				return false;
			}

			$('##frmSWLProgramSettings,##divSWProgramSettingsSaveLoading').toggle();

			var objParams = { 
				seminarID:$('##seminarID').val(),
				includeConnectionInstruction:$("##includeConnectionInstruction:checked").length,
				includeConnectionInstructionCustomEmail:$("##includeConnectionInstructionCustomEmail:checked").length,
				includeConnectionInstructionCustomText:$("##includeConnectionInstructionCustomText:checked").length,
				customEmail:customEmail,
				customText:$('##customText').val(),
				enableCustomField:$("##enableCustomField:checked").length,
				<cfif local.hasManageSWFormRights>
					preTestRequired:$('##preTestRequired').is(':checked') ? 1 : 0, 
					examRequired:$('##examRequired').is(':checked') ? 1 : 0, 
					evaluationRequired:$('##evaluationRequired').is(':checked') ? 1 : 0,
				</cfif>
				<cfif local.hasEditSWLProgramAllRights>
					isNATLE:$("##isNATLE:checked").length,
				</cfif>
				offerCertificate:$("##offerCertificate:checked").length
			};

			TS_AJX('ADMINSWL','saveSWLProgramSettings',objParams,saveResult,saveResult,20000,saveResult);
		}
		function showConnectionInstructionSettings(){
			if ($('##includeConnectionInstruction').is(':checked')) {
				$('##connectionInstructionSettingsHolder').removeClass("d-none");
			} else {
				// clear the toggles too
				$('##includeConnectionInstructionCustomEmail').prop("checked", false);
				showCustomEmail();
				$('##includeConnectionInstructionCustomText').prop("checked", false);
				showCustomText();
				$('##connectionInstructionSettingsHolder').addClass("d-none");
			}
		}
		function showCustomEmail(){
			if ($('##includeConnectionInstructionCustomEmail').is(':checked')) {
				$('##customEmailHolder').removeClass("d-none");
			} else {
				$('##customEmailHolder').addClass("d-none");
				$('##customEmail').val('');
			}
		}
		function showCustomText(){
			if ($('##includeConnectionInstructionCustomText').is(':checked')) {
				$('##customTextHolder').removeClass("d-none");
			} else {
				$('##customTextHolder').addClass("d-none");
				$('##customText').val('');
			}
		}
		function showCustomFieldSettings(){
			if ($('##enableCustomField').is(':checked')) {
				$('##customFieldHolder').removeClass("d-none");
			} else {
				$('##customFieldHolder').addClass("d-none");
			}
		}
		function showPreTestRequiredSettings(){
			if ($('##preTestRequired').is(':checked')) {
				$('##preTestRequiredHolder').removeClass("d-none");
			} else {
				$('##preTestRequiredHolder').addClass("d-none");
			}
		}
		function showExamRequiredSettings(){
			if ($('##examRequired').is(':checked')) {
				$('##examRequiredHolder').removeClass("d-none");
			} else {
				$('##examRequiredHolder').addClass("d-none");
			}
		}
		function showEvaluationRequiredSettings(){
			if ($('##evaluationRequired').is(':checked')) {
				$('##evaluationRequiredHolder').removeClass("d-none");
			} else {
				$('##evaluationRequiredHolder').addClass("d-none");
			}
		}
		function initSWLSettings(){
			<cfloop array="#local.arrSWLEnrollmentFields#" index="local.thisGrid">
				if($.fn.DataTable.isDataTable('##mccf_'+'#local.thisGrid.gridExt#'+'_table'))
					mccf_reloadFieldsTable('#local.thisGrid.gridExt#');	
				else mccf_initFieldsTable('#local.thisGrid.gridExt#');
			</cfloop>
			<cfif local.hasManageSWFormRights>
				if(!$.fn.DataTable.isDataTable('##SW_pretest_FormsTable')) 
					initSWForms('SWL','preTest');
				if(!$.fn.DataTable.isDataTable('##SW_posttest_FormsTable')) 
					initSWForms('SWL','postTest');
				if(!$.fn.DataTable.isDataTable('##SW_evaluation_FormsTable')) 
					initSWForms('SWL','evaluation');
			</cfif>
		}
		$(document).on('keypress', '##customEmail', function(event) {
			if (event.which === 13) { 
				event.preventDefault(); 
			}
		});

		<cfloop array="#local.arrSWLEnrollmentFields#" index="local.thisGrid">
			function mccf_#local.thisGrid.gridExt#_drawCallBack(row, data, dataIndex) {
				let totalRows = window['mccf_#local.thisGrid.gridExt#_table'].data().count();
				
				if (totalRows) {
					if($("##enableCustomField:checked").length == 0) {
						$("##enableCustomField").prop('checked',true);
					}
					$('##enableCustomField').prop('disabled',true);
				} else {
					$('##enableCustomField').prop('disabled',false);
				}
				showCustomFieldSettings();
				
				if (isSWProgramLocked())
					$('##enableCustomField').prop('disabled',true); 
			}
		</cfloop>
	</script>
	#local.strSWLEnrollmentFieldsGrid.js#
	<style>
		.custom-control-input:disabled~.custom-control-label {
			color: ##3b3e66;
		}
    </style>	
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.settingsJS#">    

<cfoutput>
	<div id="err_settings" class="alert alert-danger mb-2 mt-2 d-none"></div>
	
	<form name="frmSWLProgramSettings" id="frmSWLProgramSettings" method="post" onsubmit="saveSWLProgramSettings();" autocomplete="off">
		<input type="hidden" name="seminarID" id="seminarID" value="#local.qrySeminar.seminarID#">
		<div class="text-right mb-2">
			<button type="button" name="btnSaveSWLProgramSettings" class="btn btn-sm btn-primary d-none" onclick="saveSWLProgramSettings();">Save Settings</button>
		</div>

		<cfif local.hasEditSWLProgramAllRights>
			<div id="advancedSettingsHolder" class="mt-2">
				<div class="card card-box mb-1">
					<div class="card-header bg-light">
						<div class="card-header--title">
							<b>Advanced Settings <span class="superuser small"></span></b>
						</div>
					</div>
					<div class="card-body pb-3">
						<div class="form-group">
							<div class="custom-control custom-switch">
								<input type="checkbox" name="isNATLE" id="isNATLE" class="custom-control-input" <cfif val(local.qrySeminar.isNATLE) is 1> checked="checked"</cfif>>
								<label class="custom-control-label" for="isNATLE">
									This is a NATLE webinar and should be tracked internally as such.
								</label>
							</div>
						</div>
					</div>
				</div>
			</div>
		</cfif>

		<div id="atRegistrationHolder" class="mt-2">
			<div class="card card-box mb-1">
				<div class="card-header bg-light">
					<div class="card-header--title">
						<b>At the Time of Registration</b>
					</div>
				</div>
				<div class="card-body pb-3">
					<div class="form-group mb-2">
						<div class="custom-control custom-switch">
							<input type="checkbox" name="includeConnectionInstruction" id="includeConnectionInstruction" onclick="showConnectionInstructionSettings();" class="custom-control-input" <cfif val(local.qrySeminar.includeConnectionInstruction) is 1> checked="checked"</cfif>>
							<label class="custom-control-label" for="includeConnectionInstruction">
								Connection Instructions: Include instructions for accessing the webinar in the registrant's confirmation email.
								<br/><i>Note: Reminder emails sent before the program will always use the version that includes connection instructions, no matter the setting here.</i>
							</label>
			
							<div id="connectionInstructionSettingsHolder" class="pt-2 <cfif val(local.qrySeminar.includeConnectionInstruction) is not 1>d-none</cfif>">
								<div class="form-group mb-2">
									<div class="custom-control custom-switch">
										<input type="checkbox" name="includeConnectionInstructionCustomEmail" id="includeConnectionInstructionCustomEmail" onclick="showCustomEmail();" class="custom-control-input" <cfif val(local.qrySeminar.includeConnectionInstructionCustomEmail) is 1> checked="checked"</cfif>>
										<label class="custom-control-label" for="includeConnectionInstructionCustomEmail">
											Allow registrants <b>on this site</b> to reply to a custom email address after receiving connection instructions.
											<br/>Unless overridden here, the reply-to address for registrants on this site will be #arguments.event.getValue('mc_siteInfo.supportProviderEmail')#
										</label>
			
										<div class="form-group <cfif val(local.qrySeminar.includeConnectionInstructionCustomEmail) is not 1>d-none</cfif> pt-2" id="customEmailHolder">
											<div class="form-label-group">
												<input type="text" name="customEmail" id="customEmail" value="#local.qrySeminar.customEmail#" class="form-control">
												<label for="customEmail">Reply-To Address</label>
											</div>
										</div>
									</div>
								</div>
								<cfif val(local.qrySeminar.isPublished) is  1 AND val(local.qrySeminar.isOpen) is 1>
									<div class="form-group mb-2">
										<div class="custom-control custom-switch">
											<input type="checkbox" name="includeConnectionInstructionCustomText" id="includeConnectionInstructionCustomText" onclick="showCustomText();" class="custom-control-input" <cfif val(local.qrySeminar.includeConnectionInstructionCustomText) is 1> checked="checked"</cfif>>
											<label class="custom-control-label" for="includeConnectionInstructionCustomText">
												Add custom language to the registrant's connection instructions email. <i class="fa-solid fa-info-circle pl-1" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="left" data-trigger="hover" title="" data-original-title="Custom language will appear in the body of the email, before the signature line."></i>
											</label>
			
											<div class="form-group <cfif val(local.qrySeminar.includeConnectionInstructionCustomText) is not 1>d-none</cfif> pt-2" id="customTextHolder">
												<div class="form-label-group">
													<textarea name="customText" id="customText" class="form-control" rows="4">#local.qrySeminar.customText#</textarea>
													<label for="customText">Custom Text (no HTML, 1000 characters max)</label>
												</div>
											</div>
										</div>
									</div>
								</cfif>
							</div>
						</div>
					</div>
			
					<div class="form-group mt-4 mb-2">
						<div class="custom-control custom-switch">
							<input type="checkbox" name="enableCustomField" id="enableCustomField" onclick="showCustomFieldSettings();" class="custom-control-input" <cfif val(local.qrySeminar.enableCustomField) is 1> checked="checked"</cfif>>
							<label class="custom-control-label text-body" for="enableCustomField">
								Custom Fields: Add custom registration fields to collect information or require questions/statements to be answered by the registrant.
								<br/><i>Note: Registrants on this site who respond to custom fields can be downloaded from the Registrants tab by clicking "Download Registrants."</i>
							</label>
							
							<div class="form-group my-2 <cfif val(local.qrySeminar.enableCustomField) is not 1>d-none</cfif>" id="customFieldHolder">
								#local.strSWLEnrollmentFieldsGrid.html#
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		
		<cfif local.hasManageSWFormRights>
			<div id="beforeWebinarBeginsHolder" class="mt-2">
				<div class="card card-box mb-1">
					<div class="card-header bg-light">
						<div class="card-header--title">
							<b>Before the Webinar Begins</b>
						</div>
					</div>
					<div class="card-body pb-3">
						<div class="form-group mb-2">
							<div class="custom-control custom-switch">
								<input type="checkbox" name="preTestRequired" id="preTestRequired" onclick="showPreTestRequiredSettings();" class="custom-control-input">
								<label class="custom-control-label text-body" for="preTestRequired">
									Pre-Test: Require attendees to complete one or more pre-tests.
								</label>
								
								<div class="my-2" id="preTestRequiredHolder">
									<div id="err_addpretest" class="alert alert-danger mb-2 d-none"></div>
									<div class="form-label-group">
										<div class="input-group">
											<select name="fPreTest" id="fPreTest" class="form-control">
												<option value=""></option>
												<cfloop query="local.qryOrgForms">
													<option value="#local.qryOrgForms.formID#">#left(local.qryOrgForms.formTitle,100)#</option>
												</cfloop>
											</select>
											<div class="input-group-append">
												<button type="button" name="btnAddPreTest" id="btnAddPreTest" class="btn input-group-text" onclick="doAddSWPreTest();">Add Pre-Test</button>
											</div>
											<label for="fPreTest">Select Custom Pre-Test for this Program</label>
										</div>
									</div>
									<table id="SW_pretest_FormsTable" class="table table-sm table-striped table-bordered" style="width:100%">
									<thead>
										<tr>
											<th id="columnid"></th>
											<th>Selected Pre-Tests</th>
											<cfif NOT local.isSWProgramLocked>
												<th>Actions</th>
											</cfif>
										</tr>
									</thead>
									</table>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div id="afterWebinarEndsHolder" class="mt-2">
				<div class="card card-box mb-1">
					<div class="card-header bg-light">
						<div class="card-header--title">
							<b>After the Webinar Ends</b>
						</div>
					</div>
					<div class="card-body pb-3">
						<div class="form-group mb-2">
							<div class="custom-control custom-switch">
								<input type="checkbox" name="examRequired" id="examRequired" onclick="showExamRequiredSettings();" class="custom-control-input">
								<label class="custom-control-label text-body" for="examRequired">
									Exam: Require attendees to complete one or more exams.
								</label>
								
								<div class="my-2" id="examRequiredHolder">
									<div id="err_addexam" class="alert alert-danger mb-2 d-none"></div>
									<div class="form-label-group">
										<div class="input-group">
											<select name="fExam" id="fExam" class="form-control">
												<option value=""></option>
												<cfloop query="local.qryOrgForms">
													<option value="#local.qryOrgForms.formID#">#left(local.qryOrgForms.formTitle,100)#</option>
												</cfloop>
											</select>
											<div class="input-group-append">
												<button type="button" name="btnAddExam" id="btnAddExam" class="btn input-group-text" onclick="doAddSWExam();">Add Exam</button>
											</div>
											<label for="fExam">Select Custom Exam for this Program</label>
										</div>
									</div>
									<table id="SW_posttest_FormsTable" class="table table-sm table-striped table-bordered" style="width:100%">
									<thead>
										<tr>
											<th id="columnid"></th>
											<th>Selected Exams</th>
											<cfif NOT local.isSWProgramLocked>
												<th>Actions</th>
											</cfif>
										</tr>
									</thead>
									</table>
								</div>
							</div>
			
							<div class="mt-4 custom-control custom-switch">
								<input type="checkbox" name="evaluationRequired" id="evaluationRequired" onclick="showEvaluationRequiredSettings();" class="custom-control-input">
								<label class="custom-control-label text-body" for="evaluationRequired">
									Evaluation: Require attendees to complete one or more evaluations.
								</label>
								
								<div class="my-2" id="evaluationRequiredHolder">
									<div id="err_addevaluation" class="alert alert-danger mb-2 d-none"></div>
									<div class="form-label-group">
										<div class="input-group">
											<select name="fEvaluation" id="fEvaluation" class="form-control">
												<option value=""></option>
												<cfloop query="local.qryOrgSurveyForms">
													<option value="#local.qryOrgSurveyForms.formID#">#left(local.qryOrgSurveyForms.formTitle,100)#</option>
												</cfloop>
											</select>
											<div class="input-group-append">
												<button type="button" name="btnAddEvaluation" id="btnAddEvaluation" class="btn input-group-text" onclick="doAddSWEvaluation();">Add Evaluation</button>
											</div>
											<label for="fEvaluation">Select Custom Evaluation for this Program</label>
										</div>
									</div>
									<table id="SW_evaluation_FormsTable" class="table table-sm table-bordered" style="width:100%">
									<thead>
										<tr>
											<th id="columnid"></th>
											<th>Selected Evaluations</th>
											<cfif NOT local.isSWProgramLocked>
												<th>Actions</th>
											</cfif>
										</tr>
									</thead>
									</table>
								</div>
							</div>
							<div class="form-group mb-2 mt-4">
								<div class="custom-control custom-switch">
									<input type="checkbox" name="offerCertificate" id="offerCertificate" class="custom-control-input" <cfif val(local.qrySeminar.offercertificate) is 1> checked="checked"</cfif>>
									<label class="custom-control-label" for="offerCertificate">
										Certificate: Award certificates to attendees upon completion.
									</label>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		<cfelse>
			<div id="afterWebinarEndsHolder" class="mt-2">
				<div class="card card-box mb-1">
					<div class="card-header bg-light">
						<div class="card-header--title">
							<b>After the Webinar Ends</b>
						</div>
					</div>
					<div class="card-body pb-3">
						<div class="form-group mb-2 mt-4">
							<div class="custom-control custom-switch">
								<input type="checkbox" name="offerCertificate" id="offerCertificate" class="custom-control-input" <cfif val(local.qrySeminar.offercertificate) is 1> checked="checked"</cfif>>
								<label class="custom-control-label" for="offerCertificate">
									Certificate: Award certificates to attendees upon completion.
								</label>
							</div>
						</div>
					</div>
				</div>
			</div>
		</cfif>
	</form>
	
	<div id="divSWProgramSettingsSaveLoading" style="display:none;">
		<div class="text-center">
			<br/>
			<i class="fa-light fa-circle-notch fa-spin fa-3x"></i>
			<br/><br/>
			<b>Please wait while we save these program settings.</b>
			<br/>
		</div>
	</div>
</cfoutput>