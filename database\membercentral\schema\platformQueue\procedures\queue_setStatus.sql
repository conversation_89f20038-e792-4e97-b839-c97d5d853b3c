ALTER PROC dbo.queue_setStatus
@queueType varchar(25),
@itemUID uniqueidentifier,
@queueStatus varchar(30)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	
	declare @newstatus int;
	select @newstatus = qs.queueStatusID 
		from dbo.tblQueueStatuses as qs
		inner join dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = @queueType
		and qs.queueStatus = @queueStatus;

	IF @newstatus is not null
		update dbo.tblQueueItems WITH (UPDLOCK, HOLDLOCK)
		set queueStatusID = @newstatus,
			dateUpdated = getdate()
		where itemUID = @itemUID;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
