<!--- If only one format, skip step 2 --->
<cfif local.qryAvailableProductFormats.recordCount is 1>
	<cflocation url="#arguments.event.getValue('mainurl')#&sa=AddToCart&ItemID=#arguments.event.getValue('itemid',0)#&Quantity_#local.qryAvailableProductFormats.formatID#_#local.qryAvailableProductFormats.rateID#=1" addtoken="no">
</cfif>

<cfset local.store = application.mcCacheManager.sessionGetValue(keyname='store', defaultValue={})>
<cfset local.qryPurchaser = application.objMember.getMemberInfo(val(local.store[arguments.appInstanceID].CrtMemberID))>

<cfset local.stopReg = false>

<legend class="tsApp tsAppLegendTitle">Step 2 of 2: Select Product Details</legend>

<cfoutput>
<form name="step2Form" method="post" action="#arguments.event.getValue('mainurl')#" onsubmit="return doS2Validate(false)">	
<input type="hidden" name="sa" Value="AddToCart" />
<input type="Hidden" name="ItemID" value="#arguments.event.getValue('itemid',0)#" />

<div class="tsAppBodyText" style="float:right;">
	<div><b>Purchaser:</b></div>
	<div style="margin-left:20px;margin-top:6px;">
		<cfif local.qryPurchaser.hasPrefix is 1 and len(local.qryPurchaser.prefix)>#local.qryPurchaser.prefix# </cfif>#local.qryPurchaser.firstname#<cfif local.qryPurchaser.hasMiddleName is 1 and len(local.qryPurchaser.middlename)> #local.qryPurchaser.middlename#</cfif> #local.qryPurchaser.lastname# <cfif local.qryPurchaser.hasSuffix is 1 and len(local.qryPurchaser.suffix)>#local.qryPurchaser.suffix#</cfif> <cfif local.qryPurchaser.hasprofessionalsuffix is 1 and len(local.qryPurchaser.professionalsuffix)>#local.qryPurchaser.professionalsuffix#</cfif><br/>
		<cfif len(local.qryPurchaser.company)>#htmleditformat(local.qryPurchaser.company)#</cfif>
	</div>
	<br />
</div>

<cfif local.qryAvailableProductFormats.recordCount>
	<div class="tsAppHeading"><i>#htmlEditFormat(local.qryProducts.contenttitle)#</i> is available in several formats.</div>
	<br/>
	<div class="tsAppBodyText">Add one or more of the following options to your cart:</div>
	<div class="tsAppBodyText" style="margin:20px 0 0 20px;">
		<table cellspacing="0" cellpadding="4" id="chooseFmt">
		<tr><th class="tsAppBB20">Qty</th><th class="tsAppBB20">&nbsp;</th><th class="tsAppBB20">Product Format</th></tr>
		<cfloop query="local.qryAvailableProductFormats">
			<tr>
				<td>
					<input class="tsAppBodyText qty" type="text" autocomplete="off" name="Quantity_#local.qryAvailableProductFormats.formatID#_#local.qryAvailableProductFormats.rateID#" size="2" value="0"><br/>
				</td>
				<td>&nbsp;</td>
				<td>
					<b>#local.qryAvailableProductFormats.Name#</b>
					&nbsp; <i><cfif local.qryAvailableProductFormats.rate eq 0>FREE<cfelse>#dollarformat(local.qryAvailableProductFormats.rate)#<cfif arguments.event.getValue('mc_siteinfo.showCurrencyType') is 1> #arguments.event.getValue('mc_siteinfo.defaultCurrencyType')#</cfif></cfif></i>
					<cfif local.qryAvailableProductFormats.numAffirmationsIncluded gt 0>
						&nbsp; (#local.qryAvailableProductFormats.numAffirmationsIncluded# affirmation<cfif local.qryAvailableProductFormats.numAffirmationsIncluded is not 1>s</cfif> included)
					</cfif>
				</td>
			</tr>
		</cfloop>
		</table>
	</div>
	<cfsavecontent variable="local.jsValidation">
		<cfoutput>
		var qtysum = 0;
		var thisqty = 0;
		$('##chooseFmt :input.qty').each(function() {
			thisqty = parseInt($(this).val().toString().replace(/[^0-9]/g,''));
			$(this).val(thisqty);
			qtysum += thisqty;
		});
		if(qtysum==0) strErr += 'No options have been selected.<br/>';
		</cfoutput>
	</cfsavecontent>
<cfelse>
	<cfset local.jsValidation = "">
	<div class="tsAppHeading">We're Sorry...</div>
	<br/>
	<div class="tsAppBodyText">
		<b><i>#iif(len(local.qryProducts.contenttitle),DE('#htmlEditFormat(local.qryProducts.contenttitle)#'),DE('This product'))#</i> is not available for you to purchase at this time<br/>
		There are no available formats for you to select from.</b>
		<br/><br/>
		<cfset local.stopReg = true>
	</div>
</cfif>	

<br />		
	
<div class="tsAppBodyText"><div id="store2_err_div" style="display:none;margin:12px 0 0 0;"></div></div>
<br/><br/>
<div class="tsAppBodyText">
	<fieldset class="tsApp">
		<legend class="tsApp tsAppLegendTitle">Action Items</legend>
		<cfif NOT local.stopReg>
		<div>
			<button type="submit" name="btnSubmit" class="tsAppBodyText" style="width:110px;font-weight:bold;"><img src="/assets/common/images/events/plus.jpg" align="left" />Continue</button>
			Continue to add product to your shopping cart.
		</div>
		</cfif>
		<br/>
		<div>
			<button type="button" name="btnCancel" class="tsAppBodyText" style="width:110px;font-weight:bold;" onClick="cancelReg(this);"><img src="/assets/common/images/events/x.jpg" align="left" />Cancel</button>
		</div>
	</fieldset>
</div>

</form>
</cfoutput>

<cfsavecontent variable="local.step2validate">
	<cfoutput>
	<script language="javascript">
	showStep2Alert = function(msg) {
		var abox = document.getElementById('store2_err_div');
			abox.innerHTML = msg;
			abox.className = 'alert';
			abox.style.display = '';
	};
	hideStep2Alert = function() {
		var abox = document.getElementById('store2_err_div');
			abox.innerHTML = '';
			abox.style.display = 'none';
	};
	doS2Validate = function(supress) {
		var _CF_this = document.forms['step2Form'];
		strErr = '';
		#local.jsValidation#
		if (strErr.length > 0) {
			if (!supress) showStep2Alert(strErr);
			return false;
		} else {
			hideStep2Alert();
			return true;
		}
	}
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.step2validate)#">