ALTER PROC dbo.job_CallSheets_grabForProcessing
    @batchSize int
AS

    SET XACT_ABORT, NOCOUNT ON;
    BEGIN TRY
        declare @statusReady int, @statusGrabbed int;

        select @statusReady = qs.queueStatusID 
        from dbo.tblQueueStatuses as qs
        inner join dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
        where qt.queueType = 'callSheets'
        and qs.queueStatus = 'readyToProcess';

        select @statusGrabbed = qs.queueStatusID 
        from dbo.tblQueueStatuses as qs
        inner join dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
        where qt.queueType = 'callSheets'
        and qs.queueStatus = 'grabbedForProcessing';

        IF OBJECT_ID('tempdb..#tmpTblQueueItems_callSheets') IS NOT NULL 
            DROP TABLE #tmpTblQueueItems_callSheets;
        CREATE TABLE #tmpTblQueueItems_callSheets (itemUID uniqueidentifier, memberID int);

        -- dequeue in order of dateUpdated. get @batchsize members
        update cd WITH (UPDLOCK, READPAST)
        set cd.statusID = @statusGrabbed,
            cd.dateUpdated = getdate()
            OUTPUT inserted.itemUID, inserted.memberID 
            INTO #tmpTblQueueItems_callSheets
        from dbo.queue_callSheetsDetail as cd
        inner join (
            select top(@BatchSize) cd2.itemUID, cd2.memberID 
            from dbo.queue_callSheetsDetail as cd2
            where cd2.statusID = @statusReady
            order by cd2.dateUpdated, cd2.itemUID
            ) as batch on batch.itemUID = cd.itemUID and batch.memberID = cd.memberID
        where cd.statusID = @statusReady;

        -- final data
        select tmp.itemUID, tmp.memberID, c.reportID, sr.reportName, sr.otherXML, 
            sr.siteID, s.siteCode, o.orgCode
        from #tmpTblQueueItems_callSheets as tmp
        inner join dbo.queue_callSheets as c on c.itemUID = tmp.itemUID
        inner join membercentral.dbo.rpt_savedReports as sr on sr.reportID = c.reportID
        inner join membercentral.dbo.sites as s on s.siteID = sr.siteID
        inner join membercentral.dbo.organizations as o on o.orgID = s.orgID;

        IF OBJECT_ID('tempdb..#tmpTblQueueItems_callSheets') IS NOT NULL 
            DROP TABLE #tmpTblQueueItems_callSheets;
        RETURN 0;	

    END TRY
    BEGIN CATCH
        IF @@trancount > 0 ROLLBACK TRANSACTION;
        EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
        RETURN -1;
    END CATCH
GO
