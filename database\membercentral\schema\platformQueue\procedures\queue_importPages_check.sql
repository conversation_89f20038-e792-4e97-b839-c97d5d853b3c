ALTER PROC dbo.queue_importPages_check

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @issueCount INT, @timeToUse datetime, @queueTypeID INT, @queueStatusID INT, @nextQueueStatusID INT, 
		@itemAsStr VARCHAR(10), @xmlMessage XML, @errorSubject VARCHAR(400), @errorTitle varchar(400);
	SELECT @queueTypeID = queueTypeID FROM dbo.tblQueueTypes WHERE queueType = 'importPages';

	-- importPages / Processing autoreset to ReadyToProcess
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -10, GETDATE());
	SELECT @queueStatusID = queuestatusID FROM dbo.tblQueueStatuses WHERE queueTypeID = @queueTypeID AND queueStatus = 'Processing';
	SELECT @nextQueueStatusID = queuestatusID FROM dbo.tblQueueStatuses WHERE queueTypeID = @queueTypeID AND queueStatus = 'ReadyToProcess';
	SELECT @issueCount = count(itemID) FROM dbo.queue_importPages WHERE statusID = @queueStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.queue_importPages
		SET statusID = @nextQueueStatusID, 
			dateUpdated = getdate()
		WHERE statusID = @queueStatusID 
		AND dateUpdated < @timeToUse;

		SET @errorTitle = 'importPages Queue Issue';
		SET @errorSubject = 'importPages queue moved items from Processing to ReadyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- importPages / readyToProcess resend message
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -220, GETDATE());
	SELECT @queueStatusID = queuestatusID FROM dbo.tblQueueStatuses WHERE queueTypeID = @queueTypeID AND queueStatus = 'readyToProcess';
	SELECT @issueCount = count(itemID) FROM dbo.queue_importPages WHERE statusID = @queueStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.queue_importPages
		SET dateUpdated = getdate()
			OUTPUT inserted.itemID 
			INTO #tmpCheckQueueMsgs (itemAsStr)
		WHERE statusID = @queueStatusID 
		AND dateUpdated < @timeToUse;

		SELECT @itemAsStr = min(itemAsStr) FROM #tmpCheckQueueMsgs;
		while @itemAsStr is not null BEGIN
			SET @xmlMessage = '<mc i="' + @itemAsStr + '" />';
			EXEC dbo.queue_importPages_sendMessage @xmlMessage=@xmlMessage;
			SELECT @itemAsStr = min(itemAsStr) FROM #tmpCheckQueueMsgs WHERE itemAsStr > @itemAsStr;
		end

		TRUNCATE TABLE #tmpCheckQueueMsgs;

		SET @errorTitle = 'importPages Queue Issue';
		SET @errorSubject = 'importPages queue resent items in readyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- importPages / readyToNotify or grabbedForNotifying with dateupdated older than 4 hours, something else in the group must be stopping it
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(hour, -4, GETDATE())
	SELECT @queueStatusID = queuestatusID FROM dbo.tblQueueStatuses WHERE queueTypeID = @queueTypeID AND queueStatus = 'readyToNotify';
	SELECT @nextQueueStatusID = queuestatusID FROM dbo.tblQueueStatuses WHERE queueTypeID = @queueTypeID AND queueStatus = 'grabbedForNotifying';
	SELECT @issueCount = count(itemID) FROM dbo.queue_importPages WHERE statusID in (@queueStatusID,@nextQueueStatusID) AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		SET @errorTitle = 'importPages Queue Issue';
		SET @errorSubject = 'importPages queue has items in readyToNotify or grabbedForNotifying last updated more than 4 hours ago';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- importPages catchall
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(hour, -4, GETDATE())
	SELECT @issueCount = count(itemID) FROM dbo.queue_importPages WHERE dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		SET @errorTitle = 'importPages Queue Issue';
		SET @errorSubject = 'importPages queue has items last updated more than 4 hours ago';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
